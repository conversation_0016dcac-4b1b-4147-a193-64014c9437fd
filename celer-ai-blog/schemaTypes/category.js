// In schemas/category.js
import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'category',
  title: 'Category',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
    }),
    // THE TEMPLATE'S GOOD IDEA
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
    }),
    // YOUR ESSENTIAL ADDITION
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
    }),
  ],
})