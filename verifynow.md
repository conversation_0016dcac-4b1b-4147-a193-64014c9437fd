Message Central API Documentation
1 | Page
Table of Contents:
Introduction..............................................................................................................................3
Message Central’s Products........................................................................................ 3
1. Verify Now............................................................................................................ 3
2. Message Now...................................................................................................... 3
3. Integrations with CRM......................................................................................... 3
Help and Support..........................................................................................................3
Rest API Base URLs:...................................................................................................... 4
Generate Token.................................................................................................................. 4
Verify Now (OTP Authentication Product)................................................................................6
Using VerifyNow as an Authentication Service with OTP..............................................6
How to use the Verification APIs................................................................................... 6
Send OTP............................................................................................................................ 8
Validate OTP....................................................................................................................... 9
VerifyNow V3 Enhancements............................................................................................ 11
1. Customizable OTP Timeouts............................................................................... 11
2. Adjustable Pricing Models.................................................................................. 11
3. Versatile Delivery Options.................................................................................. 11
4. Implementation Use Cases................................................................................ 11
Message Now (SMS API Product)........................................................................................... 12
Use Case Scenarios.....................................................................................................12
Adjustable Pricing Models........................................................................................... 13
Message Sending Modes............................................................................................ 13
1. Send a Message to a Single Number.................................................................. 13
2. Send a Message to Multiple Numbers............................................................... 13
Send SMS.......................................................................................................................... 14
SMS Callback Integration with Message Central........................................................ 16
Message Now V3 Enhancements..................................................................................... 18
Integration with CRM...............................................................................................................19
CleverTap CRM Integration............................................................................................... 19
Prerequisites for Integration........................................................................................19
Integration Steps........................................................................................................20
Pipedrive CRM Integration............................................................................................... 23
Prerequisites for Integration.......................................................................................23
Integration Steps........................................................................................................ 23
Response Codes....................................................................................................................28
2 | Page
Introduction
Welcome to Message Central's product documentation. Here you will find details to learn
about, try, and implement each of Message Central's products!
Message Central’s Products
There are 3 services that we o er currently.
1. Verify Now
Verify Now perform phone-based, multi-factor authentication (MFA) using multiple
channels and methods. It is an authentication service using SMS, WhatsAPP, RCS or
Silent Authentication which makes it easy for you to set up multi-factor
Authentication without going through any registration process.
2. Message Now
Message Now API makes it easy to send alerts, transactional messages, otp,
reminders, notifications, marketing messages, and more using your preferred
channels. It’s a messaging API for sending messages over SMS, WhatsAPP, RCS and
Email. Send messages through a secure platform that o ers direct carrier
connections, global carrier data, and telecom fraud expertise.
3. Integrations with CRM
MessageCentral is now integrated with CRMs like Shopify, PipeDrive, CleverTap etc.
Y ou can easily integrate with these CRMs and use our services hassle-free and grow
your business. enterprises can now initiate Messages on SMS and whatsAPP through
these CRM as part of their workflow. Currently MessageCentral has integration with
i. CleverTap
ii. Pipedrive
iii. Shopify
Help and Support
For implementation support and any feedback, please reach out to us at
<EMAIL>
3 | Page
Rest API Base URLs:
All Platform API endpoints below should be prefixed with the following URL:
https://cpaas.messagecentral.com
Generate Token
When using the VerifyNow API, MessageNow API, or CRMs to send messages, the initial call
should be to the token generation API. This API returns a token that must be included in all
subsequent calls. An authentication token is needed to validate the user and should be
included in the header section of each request.
Request Parameters:
Field Type Mandatory Description
customerId String yes Customer identifier (need to login
on message central website to get
your customer id
country Integer no Country code to send OTP to
email String no Email
key String yes Base-64 encrypted password
scope String no Use ‘NEW’ for first time
Request URL Path:
/auth/v1/authentication/token
4 | Page
cURL
curl --location
'https://cpaas.messagecentral.com/auth/v1/authentication/token?customerId=<
CustomerId>&key=<Base64 Encrypted
password>&scope=NEW&country=91&email=<EMAIL>' \
--header 'accept: */*'
NOTE: To convert a cURL command into code using Postman, open Postman, import the
cURL command via the "Import" button, and then generate the code in your preferred
language by clicking the "Code" button on the right side of the request.
Response JSON:
A successful response will return a 200 status code.
{
"status": Integer,
"token": "String"
}
5 | Page
Verify Now
Mobile phone numbers serve as the ultimate user identity in the digital universe. Digital
applications, whether on the web or on mobile, verify the mobile numbers of their users to
ensure their authenticity. To address this growing need, Message Central has developed a
reliable, quick, and cost-e ective mobile number verification service called Verify Now.
Using VerifyNow as an Authentication Service with OTP
VerifyNow can be used as an authentication service by generating and sending One-Time
Passwords (OTPs). Here are the key details:
● OTP Generation and Sending: For SMS, VerifyNow generates an OTP and sends it to
the user using the predefined sender IDs. In India, the sender ID used is UTOMOB,
while for international messages, the sender ID U2 INFO is used.
For WhatsApp, VerifyNow sends it to the user using Message Central WhatsApp
account brand name and the brand name is “Message Central by U2opia Mobile.
”
● Message Template: The OTP message sent to the user will utilize Message Central’s
predefined message template. Custom sender IDs or message templates provided
by the enterprise will not be used; they will be ignored by the VerifyNow service.
Also, the following parameters need to be sent while using VerifyNow APIs;
API Parameter Type Value
flowType String SMS/WHATSAPP/RCS/SAUTH
type String OTP
How to use the Verification APIs
When you integrate with the VerifyNow API for OTP-based authentication, the following
process occurs:
1. 2. 3. The enterprise requests the VerifyNow API to generate an OTP.
VerifyNow generates the OTP and sends it to the user’s mobile number using the
predefined sender ID(in case of SMS) & Brand Name(in case of WhatsApp)
The user receives the OTP in a message formatted according to U2opia’s template.
6 | Page
4. The enterprise then uses the OTP for user verification as part of its authentication
workflow.
This ensures a consistent and reliable user experience for OTP delivery across di erent
regions.
The following diagram shows how to use the Verification APIs when using the iOS, Android
or Javascript SDKs to initiate a verification.
7 | Page
Send OTP
To sendOtp on a mobile number below are the request parameters. The authentication
token is required to send OTP which is generated by the generated token API (which you
can find above in Introduction section)
Request Header Type Mandatory
authToken String yes
Request URL Path:
POST /verification/v3/send
Request Parameters:
Field Type Mandatory Description
countryCode String yes Country code
otpLength Integer no Send a number between 4 and 8.
Default is 4
mobileNumber String yes Mobile number for single text
flowType String yes We send OTP using multiple mediums
like SMS, WhatsApp, email, etc.
For now, use SMS & WHATSAPP only
NOTE: To send OTP via SMS please use flowType as SMS & WHATSAPP in case of it.
cURL
curl --location --request POST
'https://cpaas.messagecentral.com/verification/v3/send?countryCode=91&flowT
ype=SMS&mobileNumber=9999999999'\
--header 'authToken:
eyJhbGciOiJIUzUxMiJ9.eyJzdWOiJDLTMzNDMyQTVGNDlGNzQwNCIsImlhdCI6MTcxMjExOTA0
MCwiZXhwIjox'
8 | Page
NOTE: To convert a cURL command into code using Postman, open Postman, import the
cURL command via the "Import" button, and then generate the code in your preferred
language by clicking the "Code" button on the right side of the request.
Response JSON:
A successful response will return a 200 status code.
{
"responseCode": 200,
"message": "SUCCESS"
,
"data": {
"verificationId": "xxxx"
"mobileNumber": "xxxx"
,
"responseCode": "200"
"errorMessage": null,
"timeout": "60"
,
,
"smCLI": null,
"transactionId": "xxxx"
,
}
}
Validate OTP
The validateOtp method is a REST API endpoint for validating a one-time password (OTP)
for customers.
Request Header Type Mandatory
authToken String yes
Request URL Path:
GET /verification/v3/validateOtp/
9 | Page
Request Parameters:
Field Type Mandatory Description
verificationId Long yes VerificationId from response of
/send api
code String yes otp
langId String no For multiple language support
by default is English.
For now we support English only
cURL
curl --location
'https://cpaas.messagecentral.com/verification/v3/validateOtp?&verification
Id=2949&code=1476' \
--header 'authToken:
eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJDLTMzNDMyQTVGNDlGNzQwNCIsImlhdCI6MTcxMjExOT
A0MC'
Response JSON:
A successful response will return a 200 status code.
{
"responseCode": 200,
"message": "SUCCESS"
,
"data": {
"verificationId": "xxxx"
,
"mobileNumber": "xxxx"
,
"responseCode": "200"
,
"errorMessage": null,
"verificationStatus": "VERIFICATION
"authToken": null,
"transactionId": "xxxx"
COMPLETED"
_
,
}
}
10 | Page
VerifyNow V3 Enhancements
The latest iteration of VerifyNow brings several enhancements designed to improve the
flexibility, e ciency, and e ectiveness of OTP (One-Time Password) delivery.
MessageCentral Version 3 API gives much more freedom and flexibility to partners
compared to Version 2 API. The new features are mentioned below, in addition to the
features available in Versions 1 and 2:
1. Customizable OTP Timeouts
● Flexible Timeout Settings: This version allows for the customization of OTP timeout
durations. Enterprises can set the validity period of the OTP based on their security
needs and user experience preferences.
● Enhanced Security: Customizable timeouts help balance between user
convenience and security, reducing the risk of unauthorized access by limiting the
time window for OTP use.
2. Adjustable Pricing Models
● Parameter-Based Pricing: The new version introduces the capability to adjust
pricing based on various parameters such as delivery channels. This allows
enterprises to better manage their costs and optimize their budget.
● Cost E ciency: By o ering a more granular pricing structure, VerifyNow helps
businesses to select the most cost-e ective options for their OTP delivery needs.
3. Versatile Delivery Options
● Multiple Channels: In addition to SMS, Version 3 supports OTP delivery through
various channels including WhatsApp, RCS, and more. This versatility ensures that
users can receive OTPs through their preferred communication method.
● Enhanced User Experience: Providing multiple delivery options improves user
satisfaction by accommodating di erent preferences and increasing the likelihood
of successful OTP receipt.
4. Implementation Use Cases
● Multi-Channel Strategy: Enterprises can implement a multi-channel OTP delivery
strategy, starting with SMS and falling back to WhatsApp or email if the initial
delivery fails.
● Tailored User Experience: Depending on the user demographic and regional
preferences, businesses can tailor the OTP delivery method to enhance user
engagement and satisfaction.
11 | Page
Message Now
Message Now is a versatile messaging API designed to facilitate the sending of messages
over various communication channels, including SMS, WhatsApp, RCS (Rich
Communication Services). This API provides robust features and flexible options to meet
diverse messaging needs.
Use Case Scenarios
● Promotional Campaigns: Use Message Now to send promotional messages to
customers via SMS, WhatsApp, or email, leveraging the API’s multi-channel
capabilities to maximize reach.
● Transactional Alerts: Ensure timely delivery of transactional alerts and notifications,
such as purchase confirmations or account updates, through the most appropriate
communication channel.
● OTP Verification: Implement a secure OTP delivery system for user authentication,
choosing between SMS, WhatsApp, or email based on user preferences and
security requirements.
If an enterprise wishes to generate its own OTP and utilize MessageCentral solely as
a messaging service provider, it should configure MessageNow using the following
parameters in the send API. In this case, the enterprise must provide the Sender ID
and message content in the respective API fields.
Also, the following parameters need to be sent while using MessageNow APIs;
API Parameter Type Value
flowType String SMS/WHATSAPP/RCS/SAUTH
type String SMS
messageType String OTP
NOTE: If you want to send OTP messages, Message Central recommends using Verify
Now APIs, which is specifically designed for that use case.
12 | Page
Adjustable Pricing Models
Parameter-Based Pricing: The API o ers adjustable pricing based on various parameters
such as the message volume, delivery region, and chosen communication channels. This
helps businesses manage costs e ectively and optimize their messaging budget.
Cost E ciency: By allowing granular control over pricing, Message Now ensures that
enterprises can select the most cost-e ective options tailored to their specific needs.
Message Sending Modes
Message Central o ers two primary modes for sending messages through the
MessageNow API:
1. Send a Message to a Single Number
To send a message to a single number, populate the following API fields:
● MobileNumber: The recipient's mobile number.
● Message: The text content of the message to be sent.
2. Send a Message to Multiple Numbers
There are two methods to send messages to multiple numbers:
a. Using Multipart File Upload
● File Upload: Use the API parameter (file) to upload a multipart file.
● Message: Use the API parameter (message) for the text to be sent.
● File Format: Only .xlsx files are supported.
● Customization: This method supports customized messages per user, allowing
di erent message content for each recipient listed in the file.
b. Using API Field for Multiple Numbers
● PhoneNumbers: Use the API parameter (phoneNumbers) to specify the list of
recipient numbers.(format : country<space> mobile :
Eg - 91 9816111111, 91 7715131105,.....………….)
● Message: Use the API parameter (message) for the text to be sent.
13 | Page
Di erence Between Multipart File Upload and PhoneNumbers Field:
● Multipart File Upload (.xlsx): Allows for customized messages per user. Each
recipient can receive a unique message as specified in the uploaded .xlsx file.
● PhoneNumbers Field: Sends the same text to all recipients. The message specified
in the message field will be uniformly delivered to everyone listed in the
phoneNumbers field.
Please find the Sample file format in XLXS format below:
Country Mobile var1 var2 var3
Where ‘var1’
,
‘var2’
, and ‘var3’ are placeholders for variables. If an enterprise wants to send a
customized message using file upload, the message field should be formatted as follows:
Sample message -
“Hello ##var##, your balance is ##var2## on date ##var3##.
”
Send SMS
To send an SMS to a mobile number, the following request parameters are required. An
authentication token, generated by the token generation API, is necessary to send the
SMS.
Request Header Type Mandatory
authToken String yes
Request URL Path:
POST /verification/v3/send
14 | Page
Request Parameters:
Field Type Mandatory Description
type String yes Use “SMS” while sending sms
file xlxs no This needs to be send as form data
countryCode String yes(in case of
single sms &
verify now)
Country code
otpLength Integer no
mobileNumber String yes (in case of
Single sms)
Mobile number for single text
flowType String yes Send “SMS”
messageType String no (default is
otp)
The type of message to be sent.
(Allowed values TRANSACTION,
PROMOTIONAL, OTP)
senderId String yes (in case of
sms)
The ID of the sender
phoneNumbers String no A list of phone numbers to send the
SMS
(format : country<space> mobile : 91
9876111111)
message String no The custom message to be sent
templateId String no Template Id of your approved template
entityId String no Entity Id of your Brand
NOTE: TemplateID and EntityID are required if the template is not registered with Message
Central. Also, both templateId and entityId must be present or both must be absent.
cURL
curl --location --request POST
'https://cpaas.messagecentral.com/verification/v3/send?countryCode=91&flowT
ype=SMS&mobileNumber=9999999999&senderId=UTOMOB&type=SMS&message=%3CYour%20
Message%20Template%3E&messageType=PROMOTIONAL'
\--header 'authToken:
eyJhbGciOiJIUzUxMiJ9.thisisatesttoken.IgGu7Sb4lovBSql5LIXZU3jlwPG4giAMZ2kTI
Mg_
EAaKVsVcCdpW
_
TYzyzdTd94GdEJMt5WntEGcEX4w0hITng'
15 | Page
NOTE: To convert a cURL command into code using Postman, open Postman, import the
cURL command via the "Import" button, and then generate the code in your preferred
language by clicking the "Code" button on the right side of the request.
Response JSON:
A successful response will return a 200 status code.
{
"responseCode": 200,
"message": "SUCCESS"
,
"data": {
"verificationId": "1234"
,
"mobileNumber": "9999999999"
,
"responseCode": "200"
"errorMessage": null,
"timeout": "30.0"
,
,
"smsCLI": null,
"transactionId": "1c9c56ec-5cd3-48b5-9e32-a15499fb77a2"
}
}
SMS Callback Integration with Message Central
To receive SMS callbacks and maintain them on your own system for receiving status
updates of all messages sent through our service, please follow these steps:
1. Information Required:
a. Customer ID
b. Brand Name
c. Registered Email ID
d. Callback URL
2. Submission Instructions: Please send the above information to
<EMAIL> with the subject line "SMS Callback Integration
with Message Central"
.
16 | Page
3. Callback URL Specifications: Ensure your callback URL is accessible and capable
of receiving HTTP POST requests containing JSON payloads with status updates of
sent SMS messages. Upon triggering, the endpoint will receive the following JSON
payload.
{
"apiKey": null,
"clientId": null,
"messageId": "a2e87214-d18c-4a2e-b7f4-802c4465a9b9"
"status": "DELIVRD"
,
"errorCode": 0,
"countryCode": null,
"mobile": "************"
,
"submitDate": "2023-11-02 13:59:18"
,
"doneDate": "2023-11-02 13:59:22"
}
,
4. Standard Status: These are the following status you will receive for all the messages
sent.
a. DELIVRD
b. EXPIRED
c. UNDELIV
d. UNKNOWN
e. REJECTD
For further assistance or clarification, please contact our support team at
<EMAIL>.
17 | Page
Message Now V3 Enhancements
1. 2. SMS: In India and several other countries, di erential rates apply for OTP,
Transactional, and Promotional messages. The v3 API allows partners to specify
these message types in the API parameters. Refer to the messageType field in the
Send Message section above for more details.
Multi-Channel Messaging: The same send API can now be used to send OTP,
Promotional, and Transactional messages over new mediums such as WhatsApp,
RCS, and Email, in addition to SMS. Refer to the FlowType field in the Send Message
section above for more details.
NOTE: These features is currently in Beta and is expected to be fully released by the end
of Q3 2024
18 | Page
Integration with CRM
Integrating your CRM with a text messaging platform like Message Central can significantly
enhance your customer communication and marketing capabilities. Connecting your CRM
with a text messaging tool allows both applications to 'speak' to each other, seamlessly
sharing and syncing data. This integration provides numerous benefits, especially for
customer relationship management, enabling the sending of individual and bulk text
messages directly from your CRM.
Send SMS Campaigns from Y our CRM System
Once your CRM system is integrated with an SMS platform, you can send SMS messages as
part of your sales process with just a few clicks. This integration can greatly enhance your
marketing e orts by allowing you to communicate with customers directly from your CRM.
With the ability to send text messages from within your CRM, you can centralize your
communication channels to better nurture customer relationships.
Use Automated SMS Workflows
CRM systems support workflows that can automate communications based on specific
customer actions. For example, you might want to automatically send a product review
request one week after a customer makes a purchase. By integrating your CRM with a text
messaging platform, you can include SMS in your automated workflows. This ensures
timely and relevant communication with your customers, enhancing their overall
experience and engagement.
CleverTap CRM Integration
Prerequisites for Integration
Before you begin with integration, ensure you have:
● A Message Central account with SMS permissions
● A CleverTap account
19 | Page
Integration Steps
This process involves the following three significant steps:
1. Message Central Account Details
2. The following details would be required. For this you need to create an account in
Message Central. The following parameters are required:
a. Customer ID - Y ou can get your customer Id from Message Central home
b. c. page
Email - This is the email with which you have created your account
Password - This is the password with which you have created your account
Adding Message Central Details on CleverTap Dashboard
Please follow the below steps to add Message Central details on CleverTap
Dashboard
i. Step 1- After logging into dashboard, navigate to Settings > Channels > SMS
ii. Step 2- Click on “
+Add Provider”
.(please check the image below).
20 | Page
iii. Step 3- Under Setup, enter the following details:
Field Description
Provider Select “Generic (Others)”
Nickname Uniquely identifies the provider ( Eg - Message Central)
Request Type Select POST Between (GET and POST)
Http Endpoint Enter Endpoint Url :
“https://partners-prod.messagecentral.com/partners/cent
ral/api/saveSingleSms”
iv. Step 4 - Under Header, enter the following Keys with respective Values
a. Y ou need to click on “Add Pairs”to enter 5 Keys & values listed below:
Key Value
customerId Y our Customer Id (from MessageCentral dashboard)
email Y our Email (used for MessageCentral login)
password Y our Password (used for MessageCentral password)
Content-Type application/json
account Y our Account Id (from CleverTap) eg,. 6K9-
***
-
**7Z
v. Step 5 - Under Parameters, select Type as Json and paste the below code
{
"flowType": "SMS"
,
"type": "SMS"
,
"mobileNumber": "$$To"
,
"messageText": "$$Body"
"senderId": "UTOMOB"
"mid": "$$MessageID"
,
, //this can be replace by your sender Id
}
21 | Page
vi. Step 6 - Click Save to save the details.
In case there is an error : Dynamic Variable cannot be added or deleted.
Just wait for a few minutes and click save.
3. Send a Test SMS
To ensure that the integration is successful, send a test SMS as follows:
1. Click the “Send Test SMS” before creating SMS campaigns and journeys.
(Y ou can find the ‘Send test SMS’ button above ‘Save’ button)
2. Enter the following details:
● Country Code and Mobile Number: Enter the country code and mobile
number to which you want to send the test message
● Message: Eg - Welcome to Message Central. We are delighted to have you
here! - Powered by U2opia
NOTE: Y ou need to enter your whitelisted content or message which is
approved by DLT for your SenderID
22 | Page
Pipedrive CRM Integration
Prerequisites for Integration
Before you begin with integration, ensure you have:
● A Message Central account with SMS permissions
● A Pipedrive account
Integration Steps
This process involves the following three significant steps:
1. Message Central Account Details
The following details would be required. For this you need to create an account in
Message Central. The following parameters are required:
a. b. c. Customer ID - Y ou can get customer Id from Message Central home page
Email - This is the email with which you have created your account
Password - This is the password with which you have created your account
2. Installing & Setting up Message Central app on Pipedrive
Please follow the below steps to add install Message Central app
i. Step 1- Open the Pipedrive Marketplace and search for "Message Central.
"
ii. Step 2- Click on "Authorize.
"
iii. Step 3- Then, click on "Allow and Install.
"
23 | Page
iv. Step 4 - Connecting your Pipedrive account with Message Central App
To integrate your account and start sending SMS, you need to add Message
Central credentials in the above installed app.
a. b. Go to “My apps” on top right corner of Pipedrive dashboard
In Message Central app, click on three dots and then Settings
c. In Settings tab, enter the following Keys with respective Values
Key Value
customerId Y our Customer Id (from MessageCentral dashboard)
email Y our Email (used for MessageCentral login)
password Y our Password (used for MessageCentral password)
v. Step 5 - Click Save to save the details.
24 | Page
3. Sending Single, Bulk SMS Broadcast and their Reporting
a. Sending single SMS using Message Central App
Navigate to Contacts section of Pipedrive where you can see all saved contacts
1. Click on any profile of saved contacts and his profile would open up
2. Enter the following details:
● Country Code and Mobile Number: Enter the country code and mobile
number to which you want to send the test message
● Enter the message template you want to send to your customers
For testing you can use: (Sender ID - UTOMOB
Message: Eg - Welcome to Message Central. We are delighted to have you
here! - Powered by U2opia )
NOTE: Y ou need to enter your whitelisted content or message which is
approved by DLT for your SenderID
● Click on “Send Message” and this message will get triggered to the entered
number
25 | Page
b. Sending Bulk broadcast SMS
Navigate to Contacts section of Pipedrive where you can see all saved contacts
1. Select all the profiles of the contacts you want to bulk broadcast SMS
2. After selecting contacts, click on More icon(three dots) at top right corner
3. Click on “SMS Campaign”
4. Enter the following details:
● Enter the message template you want to send to your customers
For testing you can use: (Sender ID - UTOMOB
Message: Eg - Welcome to Message Central. We are delighted to
have you here! - Powered by U2opia )
● Click on “SMS Message” and this message will get triggered to all the
contacts selected
26 | Page
c. Sending Bulk broadcast SMS
Navigate to Contacts section of Pipedrive where you can see all saved contacts
i. Click on More icon(three dots) at top right corner
ii. Click on “Campaign Report”
iii. If you have sent any campaign previously, you can check the report. All mobile
numbers will be visible and SMS status of profiles will be updated in a few minutes.
27 | Page
Response Codes
Code Description
200 SUCCESS
400 BAD_REQUEST
409 DUPLICATE_RESOURCE
500 SERVER_ERROR
501 INVALID_CUSTOMER_ID
505 INVALID_VERIFICATION_ID
506 REQUEST_ALREADY_EXISTS
511 INVALID_COUNTRY_CODE
700 VERIFICATION_FAILED
702 WRONG_OTP_PROVIDED
703 ALREADY_VERIFIED
705 VERIFICATION_EXPIRED
800 MAXIMUM_LIMIT_REACHED
28 | Page