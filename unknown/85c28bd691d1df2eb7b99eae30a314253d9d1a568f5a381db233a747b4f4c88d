import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

/**
 * Webhook endpoint for database triggers to notify admin dashboard
 * This is called by PostgreSQL triggers via pg_net.http_post
 */
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from our database (basic security)
    const authHeader = request.headers.get('authorization')
    const expectedToken = process.env.WEBHOOK_SECRET_TOKEN
    
    if (!expectedToken || authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { event_type, data } = body

    // Handle different event types
    switch (event_type) {
      case 'new_contact_request':
        await notifyAdminNewContactRequest(data)
        break
      
      case 'new_doctor_signup':
        await notifyAdminNewDoctorSignup(data)
        break
      
      default:
        console.log('Unknown event type:', event_type)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Notify admin dashboard of new contact request
 * Updated to use polling-based approach instead of real-time subscriptions
 */
async function notifyAdminNewContactRequest(data: any) {
  try {
    // Since the admin dashboard uses polling instead of real-time subscriptions,
    // we just need to log the notification. The dashboard will pick up the new
    // contact request on its next poll cycle (every 30 seconds).
    console.log('New contact request notification received:', {
      id: data.id,
      doctor_name: data.doctor_name,
      request_type: data.request_type,
      created_at: data.created_at
    })

    // Optional: You could implement additional notification mechanisms here
    // such as email notifications, Slack webhooks, etc.

  } catch (error) {
    console.error('Error processing new contact request notification:', error)
  }
}

/**
 * Notify admin dashboard of new doctor signup
 * Updated to use polling-based approach instead of real-time subscriptions
 */
async function notifyAdminNewDoctorSignup(data: any) {
  try {
    // Since the admin dashboard uses polling instead of real-time subscriptions,
    // we just need to log the notification. The dashboard will pick up the new
    // doctor signup on its next data refresh.
    console.log('New doctor signup notification received:', {
      id: data.id,
      name: data.name,
      email: data.email,
      created_at: data.created_at
    })

    // Optional: You could implement additional notification mechanisms here
    // such as email notifications, Slack webhooks, etc.

  } catch (error) {
    console.error('Error processing new doctor signup notification:', error)
  }
}
