-- <PERSON><PERSON> AI - Migration 006: Update Webhook URL
-- This script updates the webhook URL in the contact request trigger function
-- to use the correct production URL instead of the placeholder.

-- Update the trigger function with the correct webhook URL
CREATE OR REPLACE FUNCTION public.notify_contact_request_via_outbox()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    webhook_url text;
    webhook_headers jsonb;
    webhook_payload jsonb;
BEGIN
    -- Only trigger for new contact requests
    IF TG_OP = 'INSERT' THEN
        -- Build webhook URL with your actual domain
        webhook_url := 'https://app.celerai.live/api/notify/admin';
        
        -- Build headers with authentication
        -- The webhook secret will be read from the environment variable in your app
        webhook_headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || COALESCE(current_setting('app.webhook_secret_token', true), 'fallback-token')
        );
        
        -- Build payload
        webhook_payload := jsonb_build_object(
            'event_type', 'new_contact_request',
            'data', row_to_json(NEW)
        );
        
        -- Queue the webhook instead of sending directly
        PERFORM public.queue_webhook(
            p_event_type := 'new_contact_request',
            p_payload := webhook_payload,
            p_webhook_url := webhook_url,
            p_headers := webhook_headers,
            p_source_table := 'contact_requests',
            p_source_id := NEW.id
        );
        
        RAISE NOTICE 'Queued webhook for new contact request: %', NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Add comment for documentation
COMMENT ON FUNCTION public.notify_contact_request_via_outbox() IS 'Updated trigger function that queues webhooks for new contact requests with correct production URL';

-- Test the webhook outbox system by checking if there are any pending webhooks
-- This will show you if the system is working
SELECT 
    status,
    COUNT(*) as count,
    MIN(created_at) as oldest,
    MAX(created_at) as newest
FROM public.webhook_outbox 
GROUP BY status
ORDER BY status;
