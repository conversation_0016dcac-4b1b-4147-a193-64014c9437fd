import Link from 'next/link'
import { Stethoscope, Smartphone, Mic, CheckCircle, Heart, Brain, Zap, ArrowRight, Clock, Star, Shield, Users, Settings } from 'lucide-react'
import { checkSession } from '@/lib/auth/dal'
import { redirect } from 'next/navigation'

export default async function Home() {
  // Check if user is already logged in and redirect to dashboard
  const session = await checkSession()
  if (session) {
    redirect('/dashboard')
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-orange-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Stethoscope className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-400 rounded-full animate-ping"></div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-400 rounded-full"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-teal-700 bg-clip-text text-transparent">
                  Celer AI
                </h1>
                <p className="text-xs text-teal-600/80">AI-Powered Healthcare Assistant</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/login"
                className="text-slate-700 hover:text-teal-700 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-orange-100/50"
              >
                Sign In
              </Link>
              <Link
                href="/signup"
                className="bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white px-6 py-2 rounded-lg text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
        <div className="text-center">
          <div className="flex justify-center mb-8">
            <div className="flex items-center space-x-2 bg-gradient-to-r from-emerald-100 to-teal-100 border border-emerald-300/50 rounded-full px-6 py-2 shadow-sm">
              <Brain className="w-5 h-5 text-emerald-600" />
              <span className="text-emerald-700 text-sm font-medium">Powered by Advanced AI</span>
              <Star className="w-4 h-4 text-orange-500 fill-current" />
            </div>
          </div>

          <h1 className="text-4xl font-extrabold text-slate-800 sm:text-5xl md:text-6xl mb-6">
            Medical Reports
            <span className="bg-gradient-to-r from-teal-600 via-emerald-600 to-cyan-600 bg-clip-text text-transparent block mt-2">
              In Under 30 Seconds
            </span>
          </h1>
          <h2 className="mt-6 max-w-3xl mx-auto text-lg sm:text-xl text-slate-600 leading-relaxed px-4 sm:px-0">
            No typing. No assistants. Just speak or attach images → choose your template → receive a fully formatted consultation, discharge, or specialist-grade report.
          </h2>

          <div className="mt-10 flex flex-col sm:flex-row justify-center items-center gap-4">
            <Link
              href="/signup"
              className="group bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white px-6 sm:px-8 py-3 rounded-lg text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 w-full sm:w-auto justify-center"
            >
              <span className="text-center">Start Your 50-Report Free Trial</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            <Link
              href="/login"
              className="bg-white/70 hover:bg-white border border-orange-300 hover:border-teal-400 text-slate-700 px-6 sm:px-8 py-3 rounded-lg text-base sm:text-lg font-medium transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md w-full sm:w-auto text-center"
            >
              Sign In
            </Link>
          </div>

          {/* Trust indicators */}
          <div className="mt-16 flex flex-col sm:flex-row flex-wrap justify-center items-center text-slate-500 gap-4 sm:gap-8">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-orange-600" />
              <span className="text-sm">50 Reports Free</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-emerald-600" />
              <span className="text-sm">No Card Needed</span>
            </div>
            <div className="flex items-center space-x-2">
              <Zap className="w-5 h-5 text-amber-600" />
              <span className="text-sm">Under 30 Seconds</span>
            </div>
          </div>
        </div>

        {/* Key Benefits */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl sm:text-3xl font-bold text-slate-800 mb-4">
              Why Choose Us Instead of Writing It Yourself
            </h3>
            <p className="text-slate-600 text-base sm:text-lg max-w-2xl mx-auto px-4 sm:px-0">
              Save hours every day with AI-powered medical documentation
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mb-16">
            <div className="group bg-white/70 backdrop-blur-sm border border-orange-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg flex items-center justify-center mb-4 shadow-md">
                <Clock className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-slate-800 mb-3">Save Hours Every Day</h4>
              <p className="text-slate-600">Dictate live during rounds or upload your voice memo/images—no more 10–20 minutes of typing per patient.</p>
            </div>

            <div className="group bg-white/70 backdrop-blur-sm border border-orange-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mb-4 shadow-md">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-slate-800 mb-3">Always Medico-Legal Ready</h4>
              <p className="text-slate-600">Pre-built medical templates (consultation, discharge, surgery) follow standard Indian medical formats with proper diagnosis codes and follow-up instructions.</p>
            </div>

            <div className="group bg-white/70 backdrop-blur-sm border border-orange-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center mb-4 shadow-md">
                <CheckCircle className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-slate-800 mb-3">Zero Errors, Consistent Quality</h4>
              <p className="text-slate-600">Smart technology ensures correct medical terminology, proper spelling, and professional structure across every patient report—avoid costly mistakes.</p>
            </div>

            <div className="group bg-white/70 backdrop-blur-sm border border-orange-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-lg flex items-center justify-center mb-4 shadow-md">
                <Smartphone className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-slate-800 mb-3">Perfect for Busy Doctors</h4>
              <p className="text-slate-600">Create patient reports from your phone or computer—ideal for GPs, specialists, hospitals, and medical residents during rounds.</p>
            </div>

            <div className="group bg-white/70 backdrop-blur-sm border border-orange-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-600 rounded-lg flex items-center justify-center mb-4 shadow-md">
                <Star className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-slate-800 mb-3">50 Reports Free—No Card Needed</h4>
              <p className="text-slate-600">Try before you buy. See the full value immediately without any upfront payment.</p>
            </div>

            <div className="group bg-white/70 backdrop-blur-sm border border-orange-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-lg flex items-center justify-center mb-4 shadow-md">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-slate-800 mb-3">Perfect for All Healthcare Professionals</h4>
              <p className="text-slate-600">Independent doctors, hospitals, high-volume specialists (radiology, dermatology, IVF, etc.), and medical students/interns.</p>
            </div>
          </div>
        </div>

        {/* How It Works */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-slate-800 mb-4">
              How It Works (Step-By-Step)
            </h3>
            <p className="text-slate-600 text-lg max-w-2xl mx-auto">
              Simple, efficient, and powerful workflow designed for busy healthcare professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="group text-center">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -inset-2 bg-gradient-to-r from-cyan-200/30 to-teal-200/30 rounded-3xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
              </div>
              <h4 className="text-lg sm:text-xl font-semibold text-slate-800 mb-3">
                1. Sign Up in 30 Seconds
              </h4>
              <p className="text-slate-600 leading-relaxed text-sm sm:text-base">
                Enter your name and verify via OTP—no credit card required.
              </p>
            </div>

            <div className="group text-center">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Mic className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -inset-2 bg-gradient-to-r from-emerald-200/30 to-green-200/30 rounded-3xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
              </div>
              <h4 className="text-lg sm:text-xl font-semibold text-slate-800 mb-3">
                2. Upload or Record
              </h4>
              <p className="text-slate-600 leading-relaxed text-sm sm:text-base">
                Speak your notes (live or recorded) and/or attach images (scans, lab results, wound photos).
              </p>
            </div>

            <div className="group text-center">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Settings className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -inset-2 bg-gradient-to-r from-purple-200/30 to-violet-200/30 rounded-3xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
              </div>
              <h4 className="text-lg sm:text-xl font-semibold text-slate-800 mb-3">
                3. Select Template
              </h4>
              <p className="text-slate-600 leading-relaxed text-sm sm:text-base">
                Choose &ldquo;Consultation,&rdquo; &ldquo;Discharge,&rdquo; &ldquo;Operative,&rdquo; or any specialist template.
              </p>
            </div>

            <div className="group text-center">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-amber-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -inset-2 bg-gradient-to-r from-orange-200/30 to-amber-200/30 rounded-3xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
              </div>
              <h4 className="text-lg sm:text-xl font-semibold text-slate-800 mb-3">
                4. Receive Your Report
              </h4>
              <p className="text-slate-600 leading-relaxed text-sm sm:text-base">
                In under 30 seconds, download a formatted, error-free PDF ready to print or share.
              </p>
            </div>
          </div>
        </div>



        {/* Free Trial & Referral Program */}
        <div className="mt-20">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {/* Free Trial */}
            <div className="group bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-200 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                  <Star className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-xl font-bold text-slate-800 mb-2">50 Reports Free Trial</h4>
                <p className="text-slate-600 mb-3">Experience full functionality before you buy</p>
                <div className="text-3xl font-bold text-emerald-600 mb-2">₹0</div>
                <p className="text-sm text-slate-500">No credit card required</p>
              </div>
            </div>

            {/* Referral Program */}
            <div className="group bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-200 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-xl font-bold text-slate-800 mb-2">Referral Rewards</h4>
                <p className="text-slate-600 mb-3">Earn credits for every colleague you refer</p>
                <div className="text-3xl font-bold text-purple-600 mb-2">Up to ₹400</div>
                <p className="text-sm text-slate-500">Credit per successful referral</p>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-slate-800 mb-4">
              Simple, Transparent Pricing
            </h3>
            <p className="text-slate-600 text-lg max-w-2xl mx-auto">
              Choose the plan that fits your practice size
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 mb-16">
            <div className="group bg-white/70 backdrop-blur-sm border border-orange-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-2xl font-bold text-slate-800 mb-2">Starter</h4>
                <p className="text-slate-600 mb-4">Perfect for small practices</p>
                <div className="text-3xl font-bold text-blue-600 mb-2">₹499</div>
                <p className="text-sm text-slate-500 mb-4">100 Reports (₹4.99 per report)</p>
                <ul className="text-sm text-slate-600 space-y-2">
                  <li>• All consultation templates</li>
                  <li>• Mobile & desktop access</li>
                  <li>• Email support</li>
                </ul>
              </div>
            </div>

            <div className="group bg-gradient-to-br from-teal-50 to-emerald-50 border-2 border-teal-300 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-teal-600 to-emerald-600 text-white px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Zap className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-2xl font-bold text-slate-800 mb-2">Professional</h4>
                <p className="text-slate-600 mb-4">Perfect for busy practices</p>
                <div className="text-3xl font-bold text-teal-600 mb-2">₹1,999</div>
                <p className="text-sm text-slate-500 mb-4">500 Reports (₹3.99 per report)</p>
                <ul className="text-sm text-slate-600 space-y-2">
                  <li>• All consultation templates</li>
                  <li>• Priority support</li>
                  <li>• Custom templates</li>
                </ul>
              </div>
            </div>

            <div className="group bg-white/70 backdrop-blur-sm border border-orange-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-violet-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Star className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-2xl font-bold text-slate-800 mb-2">Enterprise</h4>
                <p className="text-slate-600 mb-4">For large clinics & hospitals</p>
                <div className="text-3xl font-bold text-purple-600 mb-2">₹8,000</div>
                <p className="text-sm text-slate-500 mb-4">3,000 Reports (₹2.67 per report)</p>
                <ul className="text-sm text-slate-600 space-y-2">
                  <li>• All features included</li>
                  <li>• Dedicated support</li>
                  <li>• API access</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-20 text-center">
          <div className="bg-gradient-to-r from-orange-100/80 to-amber-100/80 border border-orange-300/50 rounded-xl p-8 shadow-lg backdrop-blur-sm">
            <Heart className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-slate-800 mb-3">
              Ready to Transform Your Practice?
            </h3>
            <p className="text-slate-600 text-lg mb-6 max-w-2xl mx-auto">
              Join hundreds of healthcare providers who are already saving time and improving patient care with AI-powered medical documentation.
            </p>
            <Link
              href="/signup"
              className="inline-flex items-center bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-lg sm:text-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 space-x-2 mb-4 w-full sm:w-auto justify-center"
            >
              <span>Start Your 50-Report Free Trial</span>
              <ArrowRight className="w-5 sm:w-6 h-5 sm:h-6" />
            </Link>
            <p className="text-sm text-slate-500 text-center">No credit card required. Unlimited access to all templates for 50 reports.</p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white/70 backdrop-blur-sm border-t border-orange-200/50 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex justify-center items-center space-x-3 mb-4">
              <div className="w-6 h-6 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <Stethoscope className="w-4 h-4 text-white" />
              </div>
              <span className="text-slate-700 font-semibold">Celer AI</span>
            </div>
            <p className="text-slate-500 mb-3">
              &copy; 2025 Celer AI. Built with ❤️ for Indian healthcare providers.
            </p>
            <div className="flex justify-center items-center space-x-6 text-slate-400 text-sm">
              <Link href="/privacy" className="hover:text-teal-600 transition-colors">Privacy Policy</Link>
              <span>•</span>
              <Link href="/terms" className="hover:text-teal-600 transition-colors">Terms of Service</Link>
              <span>•</span>
              <a href="mailto:<EMAIL>" className="hover:text-teal-600 transition-colors">Support</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}