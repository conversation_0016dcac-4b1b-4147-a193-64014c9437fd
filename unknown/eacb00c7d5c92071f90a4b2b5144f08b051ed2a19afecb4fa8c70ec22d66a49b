#!/usr/bin/env node

/**
 * Make Approved Field Nullable Migration
 * Usage: node database/create/make-approved-nullable.js
 * 
 * This script:
 * 1. Removes NOT NULL constraint from approved field
 * 2. Updates admin users to have approved = NULL
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase client with service role key (bypasses RLS)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function makeApprovedNullable() {
  try {
    console.log('🔧 Making approved field nullable for admin security\n')
    
    // Step 1: Remove NOT NULL constraint
    console.log('📝 Step 1: Removing NOT NULL constraint from approved field...')
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE public.profiles ALTER COLUMN approved DROP NOT NULL;'
    })
    
    if (alterError) {
      console.error('❌ Error altering table:', alterError.message)
      process.exit(1)
    }
    console.log('✅ NOT NULL constraint removed successfully')
    
    // Step 2: Find admin users
    console.log('\n📊 Step 2: Finding admin users...')
    const { data: adminUsers, error: fetchError } = await supabase
      .from('profiles')
      .select('id, email, name, role, approved')
      .in('role', ['admin', 'super_admin'])
    
    if (fetchError) {
      console.error('❌ Error fetching admin users:', fetchError.message)
      process.exit(1)
    }
    
    if (!adminUsers || adminUsers.length === 0) {
      console.log('ℹ️  No admin users found.')
    } else {
      console.log(`📊 Found ${adminUsers.length} admin user(s):`)
      adminUsers.forEach((admin, index) => {
        console.log(`   ${index + 1}. ${admin.name} (${admin.email}) - Role: ${admin.role}`)
      })
      
      // Step 3: Update admin users
      console.log('\n🔒 Step 3: Setting approved = NULL for admin users...')
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ approved: null })
        .in('role', ['admin', 'super_admin'])
      
      if (updateError) {
        console.error('❌ Error updating admin users:', updateError.message)
        process.exit(1)
      }
      
      console.log('✅ Admin users updated successfully')
    }
    
    console.log('\n🎉 Migration completed successfully!')
    console.log('📋 New approved field logic:')
    console.log('   • NULL = Admin-only access (cannot access doctor portal)')
    console.log('   • true = Doctor access (can access doctor portal)')
    console.log('   • false = Pending approval (cannot access any portal)')
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    process.exit(1)
  }
}

// Run the migration
makeApprovedNullable()
