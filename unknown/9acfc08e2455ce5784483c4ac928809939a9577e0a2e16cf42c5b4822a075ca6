import { useState, useEffect, useCallback, useRef } from 'react'

export interface JobStatus {
  jobId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  type: string
  createdAt: string
  completedAt?: string
  error?: string
  result?: any
  metadata?: Record<string, any>
}

export interface UseJobStatusOptions {
  /**
   * Polling interval in milliseconds
   * @default 2000
   */
  pollInterval?: number
  
  /**
   * Maximum number of poll attempts
   * @default 30
   */
  maxAttempts?: number
  
  /**
   * Whether to stop polling when job is completed or failed
   * @default true
   */
  stopOnComplete?: boolean
  
  /**
   * Callback when job status changes
   */
  onStatusChange?: (status: JobStatus) => void
  
  /**
   * Callback when job completes successfully
   */
  onComplete?: (result: any) => void
  
  /**
   * Callback when job fails
   */
  onError?: (error: string) => void
}

export interface UseJobStatusReturn {
  jobStatus: JobStatus | null
  isPolling: boolean
  error: string | null
  startPolling: (jobId: string) => void
  stopPolling: () => void
  refetch: () => Promise<void>
}

/**
 * Hook for polling job status - Part of UI Reconciliation for Optimistic Updates
 * 
 * This hook allows components to track the status of background operations
 * after optimistic updates, ensuring UI consistency with database state.
 * 
 * @example
 * ```tsx
 * const { jobStatus, startPolling, isPolling } = useJobStatus({
 *   onComplete: (result) => {
 *     // Update UI with confirmed result
 *     setQuotaUsed(result.newQuotaUsed)
 *   },
 *   onError: (error) => {
 *     // Revert optimistic update
 *     setQuotaUsed(previousQuotaUsed)
 *     showError(error)
 *   }
 * })
 * 
 * // After making optimistic update and getting jobId from response headers
 * const jobId = response.headers.get('X-Job-ID')
 * if (jobId) {
 *   startPolling(jobId)
 * }
 * ```
 */
export function useJobStatus(options: UseJobStatusOptions = {}): UseJobStatusReturn {
  const {
    pollInterval = 2000,
    maxAttempts = 30,
    stopOnComplete = true,
    onStatusChange,
    onComplete,
    onError
  } = options

  const [jobStatus, setJobStatus] = useState<JobStatus | null>(null)
  const [isPolling, setIsPolling] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const pollCountRef = useRef(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const currentJobIdRef = useRef<string | null>(null)

  const fetchJobStatus = useCallback(async (jobId: string): Promise<JobStatus | null> => {
    try {
      const response = await fetch(`/api/job-status?id=${encodeURIComponent(jobId)}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Job not found or expired')
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch job status')
      }

      return data.data as JobStatus
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      throw err
    }
  }, [])

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    setIsPolling(false)
    pollCountRef.current = 0
    currentJobIdRef.current = null
  }, [])

  const startPolling = useCallback((jobId: string) => {
    // Stop any existing polling
    stopPolling()
    
    // Reset state
    setError(null)
    pollCountRef.current = 0
    currentJobIdRef.current = jobId
    setIsPolling(true)

    const poll = async () => {
      try {
        pollCountRef.current += 1
        
        const status = await fetchJobStatus(jobId)
        if (!status) return

        setJobStatus(status)
        onStatusChange?.(status)

        // Check if job is complete
        if (status.status === 'completed') {
          onComplete?.(status.result)
          if (stopOnComplete) {
            stopPolling()
          }
          return
        }

        if (status.status === 'failed') {
          onError?.(status.error || 'Job failed')
          if (stopOnComplete) {
            stopPolling()
          }
          return
        }

        // Check if we've reached max attempts
        if (pollCountRef.current >= maxAttempts) {
          setError(`Polling timeout: Maximum attempts (${maxAttempts}) reached`)
          stopPolling()
          return
        }

      } catch (err) {
        console.error('Job status polling error:', err)
        
        // Stop polling on persistent errors
        if (pollCountRef.current >= 3) {
          stopPolling()
        }
      }
    }

    // Start polling immediately, then at intervals
    poll()
    intervalRef.current = setInterval(poll, pollInterval)
  }, [fetchJobStatus, maxAttempts, pollInterval, stopOnComplete, onStatusChange, onComplete, onError, stopPolling])

  const refetch = useCallback(async () => {
    if (!currentJobIdRef.current) {
      throw new Error('No active job to refetch')
    }

    try {
      const status = await fetchJobStatus(currentJobIdRef.current)
      if (status) {
        setJobStatus(status)
        onStatusChange?.(status)
      }
    } catch (err) {
      console.error('Job status refetch error:', err)
      throw err
    }
  }, [fetchJobStatus, onStatusChange])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling()
    }
  }, [stopPolling])

  return {
    jobStatus,
    isPolling,
    error,
    startPolling,
    stopPolling,
    refetch
  }
}

/**
 * Simplified hook for one-time job status check
 */
export function useJobStatusOnce(jobId: string | null) {
  const [status, setStatus] = useState<JobStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!jobId) return

    const fetchStatus = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const response = await fetch(`/api/job-status?id=${encodeURIComponent(jobId)}`)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        if (!data.success) {
          throw new Error(data.error || 'Failed to fetch job status')
        }

        setStatus(data.data)
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
        setError(errorMessage)
      } finally {
        setLoading(false)
      }
    }

    fetchStatus()
  }, [jobId])

  return { status, loading, error }
}
