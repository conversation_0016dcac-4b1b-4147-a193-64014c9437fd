import { createBrowserClient } from '@supabase/ssr'
import { Database } from '@/lib/types'

export function createClient() {
  const client = createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  // Handle refresh token errors gracefully
  client.auth.onAuthStateChange((event, session) => {
    if (event === 'TOKEN_REFRESHED' && !session) {
      // If token refresh failed, clear the session
      console.log('Token refresh failed, clearing session')
      client.auth.signOut()
    }
  })

  return client
}
