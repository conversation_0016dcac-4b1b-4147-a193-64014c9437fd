# Database Creation Scripts

This directory contains scripts for creating users and testing database functionality.

## Scripts

### 1. Create Admin User (`create-admin-user.js`)

Creates an admin user in Supabase with proper authentication and profile setup.

**Usage:**
```bash
npm run create-admin
```

**What it does:**
1. Creates a user in Supabase Auth
2. Creates a profile in the `profiles` table
3. Sets up proper admin permissions
4. Auto-confirms the email
5. Sets admin-specific settings (no quota/billing needed)

**Interactive prompts:**
- Admin name
- Admin email
- Admin password (hidden input)
- Role (admin/super_admin)

### 2. Test Email Validation (`test-email-validation.js`)

Tests if an email address is valid according to Supabase's validation rules.

**Usage:**
```bash
node database/create/test-email-validation.js
```

**What it does:**
1. Tests email validation using Supabase admin API
2. Creates a temporary user to test validation
3. Cleans up the test user automatically
4. Reports validation results

## Troubleshooting

### Email Validation Issues

If you're getting "Email address invalid" errors:

1. **Test the email first:**
   ```bash
   node database/create/test-email-validation.js
   ```

2. **Common issues:**
   - Email format doesn't meet Supabase requirements
   - Domain restrictions in Supabase project settings
   - Special characters in email address

3. **Check Supabase project settings:**
   - Go to Authentication > Settings in Supabase dashboard
   - Check "Email validation" settings
   - Verify domain allowlist/blocklist

### Database Connection Issues

If scripts fail to connect:

1. **Check environment variables:**
   ```bash
   # Make sure these are set in .env.local
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

2. **Verify Supabase project:**
   - Project is active and not paused
   - Service role key has correct permissions
   - Database is accessible

### Permission Issues

If you get RLS (Row Level Security) errors:

1. **Service role key:** The scripts use the service role key which bypasses RLS
2. **Database policies:** Ensure your RLS policies allow the operations
3. **Table permissions:** Check that the service role has access to required tables

## Security Notes

- **Service Role Key:** These scripts use the service role key which has full database access
- **Password Security:** Passwords are hidden during input but stored in plain text in Supabase Auth
- **Admin Creation:** Only run these scripts in secure environments
- **Cleanup:** Test scripts automatically clean up temporary data

## Requirements

- Node.js
- npm packages: `@supabase/supabase-js`, `dotenv`, `readline`
- Valid Supabase project with service role key
- Proper environment variables in `.env.local`
