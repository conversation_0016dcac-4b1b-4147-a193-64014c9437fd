'use client'

import { useState, useEffect } from 'react'
import { ReferredWelcomeModal } from './referred-welcome-modal'
import { DashboardFeedbackWidget } from '@/components/feedback/dashboard-feedback-widget'
import { createClient } from '@/lib/supabase/client'
import { ReferralInfo, Profile } from '@/lib/types'

interface DashboardClientProps {
  doctorId: string
  user?: Profile | null
}

export function DashboardClient({ doctorId, user }: DashboardClientProps) {
  const [showWelcomeModal, setShowWelcomeModal] = useState(false)
  const [referralInfo, setReferralInfo] = useState<ReferralInfo | null>(null)
  const [mounted, setMounted] = useState(false)

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return // Only run on client side

    const checkWelcomeModal = async () => {
      // Check if user has seen welcome modal before
      const hasSeenWelcome = localStorage.getItem(`welcome_seen_${doctorId}`)

      if (!hasSeenWelcome && user?.referred_by) {
        // User was referred - get minimal referrer info for modal
        const supabase = createClient()
        const { data: referrer } = await supabase
          .from('profiles')
          .select('name, referral_code')
          .eq('id', user.referred_by)
          .single()

        if (referrer) {
          // Create minimal referral info for modal
          const modalReferralInfo: ReferralInfo = {
            referral_code: user.referral_code || '',
            total_referrals: 0,
            successful_referrals: 0,
            pending_referrals: 0,
            discount_earned: 0,
            referred_by: {
              name: referrer.name,
              referral_code: referrer.referral_code || ''
            },
            recent_referrals: []
          }
          setReferralInfo(modalReferralInfo)
          setShowWelcomeModal(true)
        }
      }
    }

    checkWelcomeModal()
  }, [doctorId, mounted, user])

  const handleCloseWelcome = () => {
    setShowWelcomeModal(false)
    // Mark as seen so it doesn't show again
    if (typeof window !== 'undefined') {
      localStorage.setItem(`welcome_seen_${doctorId}`, 'true')
    }
  }

  // Don't render anything until mounted on client
  if (!mounted) {
    return null
  }

  return (
    <>
      <ReferredWelcomeModal
        isOpen={showWelcomeModal}
        onClose={handleCloseWelcome}
        referralInfo={referralInfo}
      />

      {/* Feedback Widget - Following navbar isolation principles */}
      <DashboardFeedbackWidget
        user={user ? {
          id: doctorId,
          name: user.name || undefined,
          email: user.email || undefined
        } : null}
      />
    </>
  )
}