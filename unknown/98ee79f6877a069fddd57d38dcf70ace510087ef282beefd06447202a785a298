-- Celer AI - Final Migration Script for Profile Unification v3.0
-- Objective: Prepare the unified 'profiles' table to accommodate all user roles
-- by making doctor-specific fields nullable.
-- CTO Approved: Database as Muscle, Application as Brain architecture

-- CORRECTED AND COMPREHENSIVE ALTER TABLE STATEMENT
-- Make ALL doctor-specific fields nullable to ensure unified profiles table flexibility
ALTER TABLE public.profiles
  -- <PERSON>uota fields
  ALTER COLUMN monthly_quota DROP NOT NULL,
  ALTER COLUMN quota_used DROP NOT NULL,
  ALTER COLUMN quota_reset_at DROP NOT NULL,

  -- Billing fields
  ALTER COLUMN billing_status DROP NOT NULL,
  ALTER COLUMN last_payment_date DROP NOT NULL,
  ALTER COLUMN next_billing_date DROP NOT NULL,
  ALTER COLUMN trial_ends_at DROP NOT NULL,
  ALTER COLUMN current_plan_id DROP NOT NULL,
  ALTER COLUMN available_discount_amount DROP NOT NULL,

  -- Referral fields
  ALTER COLUMN referral_discount_earned DROP NOT NULL,
  ALTER COLUMN total_referrals DROP NOT NULL,
  ALTER COLUMN successful_referrals DROP NOT NULL,
  ALTER COLUMN conversion_date DROP NOT NULL;

-- THE FINAL, SIMPLIFIED TRIGGER FUNCTION
-- Responsibility: Bridge auth.users to public.profiles with minimal logic
-- Business logic belongs in application layer, not database
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  -- Create a basic profile, pulling name from the user metadata.
  -- The table's DEFAULT values will handle the rest for the common 'doctor' path.
  INSERT INTO public.profiles (id, email, name)
  VALUES (new.id, new.email, new.raw_user_meta_data->>'name');

  -- If a role was passed in metadata (e.g., during admin creation), update the role.
  -- This makes our admin creation script a clean, two-step process handled atomically.
  IF new.raw_user_meta_data->>'role' IS NOT NULL THEN
    UPDATE public.profiles
    SET role = (new.raw_user_meta_data->>'role')::public.user_role
    WHERE id = new.id;
  END IF;

  RETURN new;
END;
$$;

-- Secure the function with a fixed search path
ALTER FUNCTION public.handle_new_user() SET search_path = public;

COMMENT ON TABLE public.profiles IS 'Unified table for all user roles. Doctor-specific fields are nullable to accommodate admins. Business logic enforced in application layer.';
