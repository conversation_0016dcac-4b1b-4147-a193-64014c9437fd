/**
 * Type Guard Enforcement Examples
 * 
 * This file demonstrates the mandatory use of type guards when accessing
 * doctor-specific fields on Profile objects. This is part of the CTO's
 * directive to ensure type safety and prevent runtime errors.
 * 
 * ALL code reviews must enforce these patterns.
 */

import { Profile, DoctorProfile, AdminProfile, QuotaInfo, BillingStatus } from '@/lib/types'
import { isDoctorProfile, isAdminProfile, getQuotaInfo, getBillingStatus } from '@/lib/guards'

// ============================================================================
// ❌ WRONG: Direct access to doctor-specific fields without type guards
// ============================================================================

/**
 * ❌ BAD EXAMPLE - This will cause TypeScript errors and runtime issues
 * Never access doctor-specific fields directly on a Profile object
 */
function badQuotaDisplay(profile: Profile): string {
  // ❌ TypeScript error: Property 'monthly_quota' may be null
  // ❌ Runtime error: Will crash for admin users
  // return `${profile.quota_used}/${profile.monthly_quota}` // DON'T DO THIS!
  
  // This is what NOT to do - it's unsafe and will fail code review
  return 'This is a bad example - see correct examples below'
}

/**
 * ❌ BAD EXAMPLE - Unsafe billing status access
 */
function badBillingStatusCheck(profile: Profile): boolean {
  // ❌ TypeScript error: Property 'billing_status' may be null
  // ❌ Runtime error: Will crash for admin users
  // return profile.billing_status === 'active' // DON'T DO THIS!
  
  return false // Placeholder to prevent compilation error
}

// ============================================================================
// ✅ CORRECT: Using type guards for safe field access
// ============================================================================

/**
 * ✅ CORRECT EXAMPLE - Safe quota display with type guards
 * This is the mandatory pattern for all quota-related code
 */
function correctQuotaDisplay(profile: Profile): string {
  if (isDoctorProfile(profile)) {
    // ✅ TypeScript knows these fields are numbers, not null
    // ✅ Safe at runtime - will only execute for doctor profiles
    return `${profile.quota_used}/${profile.monthly_quota}`
  }
  
  // ✅ Graceful handling for non-doctor profiles
  return 'N/A (Admin)'
}

/**
 * ✅ CORRECT EXAMPLE - Safe billing status check with type guards
 */
function correctBillingStatusCheck(profile: Profile): boolean {
  if (isDoctorProfile(profile)) {
    // ✅ TypeScript knows billing_status is not null
    // ✅ Safe at runtime
    return profile.billing_status === 'active'
  }
  
  // ✅ Admins don't have billing status
  return false
}

/**
 * ✅ CORRECT EXAMPLE - Using utility functions from guards.ts
 * This is the preferred approach for common operations
 */
function correctQuotaDisplayWithUtility(profile: Profile): string {
  const quotaInfo = getQuotaInfo(profile)
  
  if (quotaInfo) {
    return `${quotaInfo.quota_used}/${quotaInfo.monthly_quota} (${quotaInfo.quota_percentage}%)`
  }
  
  return 'N/A (Admin)'
}

/**
 * ✅ CORRECT EXAMPLE - Complex quota logic with type guards
 */
function correctQuotaWarningCheck(profile: Profile): {
  shouldWarn: boolean
  warningLevel: 'none' | 'low' | 'medium' | 'high' | 'critical'
  message: string
} {
  if (!isDoctorProfile(profile)) {
    return {
      shouldWarn: false,
      warningLevel: 'none',
      message: 'Admin users do not have quota limits'
    }
  }

  // ✅ Safe to access doctor-specific fields after type guard
  const percentage = (profile.quota_used / profile.monthly_quota) * 100

  if (percentage >= 100) {
    return {
      shouldWarn: true,
      warningLevel: 'critical',
      message: 'Quota exceeded! Contact admin immediately.'
    }
  } else if (percentage >= 90) {
    return {
      shouldWarn: true,
      warningLevel: 'high',
      message: 'Quota almost exhausted. Consider contacting admin.'
    }
  } else if (percentage >= 75) {
    return {
      shouldWarn: true,
      warningLevel: 'medium',
      message: 'Quota usage is high. Monitor your usage.'
    }
  } else if (percentage >= 50) {
    return {
      shouldWarn: true,
      warningLevel: 'low',
      message: 'You have used half of your monthly quota.'
    }
  }

  return {
    shouldWarn: false,
    warningLevel: 'none',
    message: 'Quota usage is within normal limits.'
  }
}

// ============================================================================
// ✅ CORRECT: React Component Examples
// ============================================================================

/**
 * ✅ CORRECT EXAMPLE - React component with proper type guards
 * This pattern must be used in all components that handle Profile objects
 */
interface ProfileDisplayProps {
  profile: Profile
}

export function ProfileDisplay({ profile }: ProfileDisplayProps) {
  // ✅ Use type guards to determine what to render
  if (isDoctorProfile(profile)) {
    return (
      <div className="doctor-profile">
        <h2>{profile.name} (Doctor)</h2>
        <p>Email: {profile.email}</p>
        <p>Clinic: {profile.clinic_name}</p>
        
        {/* ✅ Safe to access doctor-specific fields */}
        <div className="quota-info">
          <p>Quota: {profile.quota_used}/{profile.monthly_quota}</p>
          <p>Status: {profile.billing_status}</p>
        </div>
      </div>
    )
  }

  if (isAdminProfile(profile)) {
    return (
      <div className="admin-profile">
        <h2>{profile.name} (Admin)</h2>
        <p>Email: {profile.email}</p>
        <p>Role: {profile.role}</p>
        
        {/* ✅ No attempt to access doctor-specific fields */}
        <p>Admin users have unlimited access</p>
      </div>
    )
  }

  // ✅ Fallback for unknown profile types
  return (
    <div className="unknown-profile">
      <h2>{profile.name}</h2>
      <p>Email: {profile.email}</p>
      <p>Unknown profile type</p>
    </div>
  )
}

// ============================================================================
// ✅ CORRECT: Server Action Examples
// ============================================================================

/**
 * ✅ CORRECT EXAMPLE - Server action with type guard validation
 * This pattern must be used in all server actions that modify doctor data
 */
export async function updateDoctorQuota(
  profileId: string, 
  newQuota: number
): Promise<{ success: boolean; error?: string }> {
  try {
    // First, fetch the profile
    const { createClient } = await import('@/lib/supabase/server')
    const supabase = await createClient()
    
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', profileId)
      .single()

    if (fetchError || !profile) {
      return { success: false, error: 'Profile not found' }
    }

    // ✅ MANDATORY: Use type guard before accessing doctor fields
    if (!isDoctorProfile(profile)) {
      return { 
        success: false, 
        error: 'Cannot update quota for non-doctor profile' 
      }
    }

    // ✅ Safe to proceed with doctor-specific operations
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ monthly_quota: newQuota })
      .eq('id', profileId)

    if (updateError) {
      return { success: false, error: 'Failed to update quota' }
    }

    return { success: true }

  } catch (error) {
    return { 
      success: false, 
      error: 'Internal server error' 
    }
  }
}

// ============================================================================
// ✅ CORRECT: Utility Functions for Common Patterns
// ============================================================================

/**
 * ✅ CORRECT EXAMPLE - Utility function for safe profile operations
 * These patterns should be extracted into reusable utilities
 */
export function getProfileDisplayData(profile: Profile) {
  const baseData = {
    id: profile.id,
    name: profile.name,
    email: profile.email,
    role: profile.role,
    approved: profile.approved
  }

  if (isDoctorProfile(profile)) {
    return {
      ...baseData,
      type: 'doctor' as const,
      quotaInfo: {
        monthly_quota: profile.monthly_quota,
        quota_used: profile.quota_used,
        quota_percentage: (profile.quota_used / profile.monthly_quota) * 100,
        billing_status: profile.billing_status
      },
      clinicInfo: {
        clinic_name: profile.clinic_name,
        phone: profile.phone
      }
    }
  }

  if (isAdminProfile(profile)) {
    return {
      ...baseData,
      type: 'admin' as const,
      adminLevel: profile.role
    }
  }

  return {
    ...baseData,
    type: 'unknown' as const
  }
}

/**
 * ✅ CORRECT EXAMPLE - Type-safe profile validation
 */
export function validateProfileForOperation(
  profile: Profile, 
  operation: 'quota_update' | 'billing_change' | 'clinic_update'
): { valid: boolean; error?: string } {
  switch (operation) {
    case 'quota_update':
    case 'billing_change':
      if (!isDoctorProfile(profile)) {
        return { 
          valid: false, 
          error: `${operation} is only available for doctor profiles` 
        }
      }
      return { valid: true }

    case 'clinic_update':
      if (!isDoctorProfile(profile)) {
        return { 
          valid: false, 
          error: 'Clinic information is only available for doctor profiles' 
        }
      }
      return { valid: true }

    default:
      return { valid: false, error: 'Unknown operation' }
  }
}

// ============================================================================
// 📋 CODE REVIEW CHECKLIST
// ============================================================================

/**
 * CODE REVIEW CHECKLIST - Mandatory checks for all PRs:
 * 
 * ✅ 1. No direct access to doctor-specific fields without type guards
 * ✅ 2. All Profile objects checked with isDoctorProfile() before accessing:
 *        - monthly_quota, quota_used, quota_reset_at
 *        - billing_status, trial_ends_at
 *        - clinic_name, phone, referral_code
 *        - Any other nullable fields specific to doctors
 * 
 * ✅ 3. Graceful handling of admin profiles (no quota/billing access)
 * ✅ 4. Use utility functions from guards.ts when possible
 * ✅ 5. Server actions validate profile type before database operations
 * ✅ 6. React components handle both doctor and admin profile types
 * ✅ 7. No TypeScript errors related to nullable fields
 * ✅ 8. Proper error messages for invalid profile types
 * 
 * REJECT any PR that violates these patterns!
 */
