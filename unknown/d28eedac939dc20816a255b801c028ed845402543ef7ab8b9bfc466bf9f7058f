#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to test email validation in Supabase
 * Usage: node database/create/test-email-validation.js
 */

const { createClient } = require('@supabase/supabase-js')
const readline = require('readline')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve)
  })
}

async function testEmailValidation() {
  try {
    const email = await askQuestion('Enter email to test: ')
    
    console.log(`\n🧪 Testing email validation for: ${email}`)
    
    // Test with admin.createUser (bypasses some validations)
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password: 'TempPassword123!',
      email_confirm: true
    })
    
    if (authError) {
      console.error('❌ Email validation failed:', authError.message)
      console.error('   Error code:', authError.code)
      console.error('   Status:', authError.status)
    } else {
      console.log('✅ Email is valid!')
      console.log('   User ID:', authData.user.id)
      
      // Clean up - delete the test user
      await supabase.auth.admin.deleteUser(authData.user.id)
      console.log('🧹 Test user cleaned up')
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
  } finally {
    rl.close()
  }
}

testEmailValidation()
