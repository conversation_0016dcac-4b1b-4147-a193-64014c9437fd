export interface VerifyNowSendResponse {
  responseCode: number
  message: string
  data: {
    verificationId: string
    mobileNumber: string
    responseCode: string
    timeout: string
    transactionId: string
  }
}

export interface VerifyNowVerifyResponse {
  responseCode: number
  message: string
  data: {
    verificationId: number
    mobileNumber: string
    verificationStatus: string
    responseCode: string
    errorMessage: string | null
    transactionId: string
  }
}

export interface SMSResult {
  success: boolean
  verificationId?: string
  transactionId?: string
  error?: string
}

export interface VerifyResult {
  success: boolean
  verified?: boolean
  error?: string
}

export class VerifyNowService {
  private authToken: string
  private baseUrl = 'https://cpaas.messagecentral.com'

  constructor(authToken: string) {
    this.authToken = authToken
  }

  // Send OTP using VerifyNow
  async sendOTP(phone: string): Promise<SMSResult> {
    try {
      // Validate phone number (should be 10 digits)
      const cleanPhone = phone.replace(/\D/g, '')
      if (cleanPhone.length !== 10) {
        return { success: false, error: 'Invalid phone number format' }
      }

      const url = `${this.baseUrl}/verification/v3/send?countryCode=91&flowType=SMS&mobileNumber=${cleanPhone}&otpLength=4`

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'authToken': this.authToken,
          'accept': '*/*'
        }
      })

      const data: VerifyNowSendResponse = await response.json()

      if (data.responseCode === 200) {
        return {
          success: true,
          verificationId: data.data.verificationId,
          transactionId: data.data.transactionId
        }
      } else {
        // Handle specific error codes
        let errorMessage = data.message || 'Failed to send OTP'

        if (response.status === 500 && errorMessage.includes('REQUEST_ALREADY_EXISTS')) {
          errorMessage = 'REQUEST_ALREADY_EXISTS'
        }

        return {
          success: false,
          error: errorMessage
        }
      }
    } catch (error) {
      console.error('VerifyNow Send Error:', error)
      return {
        success: false,
        error: 'Network error while sending OTP'
      }
    }
  }

  // Verify OTP using VerifyNow
  async verifyOTP(verificationId: string, otp: string): Promise<VerifyResult> {
    try {
      // Validate OTP (should be 4 digits)
      if (!/^\d{4}$/.test(otp)) {
        return { success: false, error: 'OTP must be 4 digits' }
      }

      const url = `${this.baseUrl}/verification/v3/validateOtp?verificationId=${verificationId}&code=${otp}`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'authToken': this.authToken,
          'accept': '*/*'
        }
      })

      const data: VerifyNowVerifyResponse = await response.json()

      if (data.responseCode === 200 && data.data?.verificationStatus === 'VERIFICATION_COMPLETED') {
        return {
          success: true,
          verified: true
        }
      } else {
        return {
          success: false,
          verified: false,
          error: data.data?.errorMessage || data.message || 'Invalid or expired OTP'
        }
      }
    } catch (error) {
      console.error('VerifyNow Verify Error:', error)
      return {
        success: false,
        verified: false,
        error: 'Network error while verifying OTP'
      }
    }
  }
}

// Factory function to create VerifyNow service
export function createVerifyNowService(): VerifyNowService {
  const authToken = process.env.VERIFYNOW_AUTH_TOKEN

  if (!authToken) {
    throw new Error('VERIFYNOW_AUTH_TOKEN environment variable is required')
  }

  return new VerifyNowService(authToken)
}

// Utility function to format phone number
export function formatPhoneNumber(phone: string): string {
  // Remove all non-digits
  const cleaned = phone.replace(/\D/g, '')
  
  // Remove country code if present (91 for India)
  if (cleaned.startsWith('91') && cleaned.length === 12) {
    return cleaned.substring(2)
  }
  
  return cleaned
}

// Validate Indian phone number
export function isValidIndianPhone(phone: string): boolean {
  const cleaned = formatPhoneNumber(phone)
  
  // Should be exactly 10 digits and start with 6, 7, 8, or 9
  return /^[6-9]\d{9}$/.test(cleaned)
}
