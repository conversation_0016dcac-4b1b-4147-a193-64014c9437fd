-- Consultation Stats View Migration
-- Creates optimized view for doctor consultation statistics
-- Replaces inefficient full-table scans with SQL-level aggregation

-- Drop existing view if it exists
DROP VIEW IF EXISTS public.doctor_consultation_stats;

-- Create optimized consultation stats view
CREATE VIEW public.doctor_consultation_stats
WITH (security_invoker = true) AS
SELECT 
    doctor_id,
    COUNT(*) as total_consultations,
    COUNT(*) FILTER (WHERE status = 'pending_generation') as pending_consultations,
    COUNT(*) FILTER (WHERE status = 'generated') as generated_consultations,
    COUNT(*) FILTER (WHERE status = 'approved') as approved_consultations,
    COUNT(*) FILTER (WHERE status = 'archived') as archived_consultations,
    COUNT(*) FILTER (WHERE created_at::date = CURRENT_DATE) as today_consultations,
    COUNT(*) FILTER (WHERE created_at >= date_trunc('week', CURRENT_DATE)) as this_week_consultations,
    COUNT(*) FILTER (WHERE created_at >= date_trunc('month', CURRENT_DATE)) as this_month_consultations,
    MAX(created_at) as last_consultation_date,
    MIN(created_at) as first_consultation_date
FROM public.consultations 
WHERE doctor_id IS NOT NULL
GROUP BY doctor_id;

COMMENT ON VIEW public.doctor_consultation_stats IS 'Optimized view for doctor consultation statistics. Provides real-time aggregated stats without full table scans.';

-- Create index on doctor_id for fast lookups (if not already exists)
CREATE INDEX IF NOT EXISTS idx_consultations_doctor_stats ON public.consultations (doctor_id, status, created_at);

-- Grant appropriate permissions
GRANT SELECT ON public.doctor_consultation_stats TO authenticated;
GRANT SELECT ON public.doctor_consultation_stats TO service_role;
