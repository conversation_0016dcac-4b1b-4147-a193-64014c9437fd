import { createClient } from '@sanity/client'
import imageUrlBuilder from '@sanity/image-url'

/**
 * Sanity CMS Client Configuration - Celer AI
 * 
 * PERFORMANCE OPTIMIZATIONS:
 * - Lazy loading: Client only created when needed
 * - CDN optimization: Uses Sanity's global CDN
 * - Caching: Built-in HTTP caching for static content
 * 
 * SECURITY:
 * - Read-only token for public content
 * - No write permissions from frontend
 * - Content sanitization in components
 */

// Lazy client creation to avoid blocking initial page load
let sanityClient: ReturnType<typeof createClient> | null = null
let imageBuilder: ReturnType<typeof imageUrlBuilder> | null = null

export function getSanityClient() {
  if (!sanityClient) {
    sanityClient = createClient({
      projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || '',
      dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
      apiVersion: '2024-01-01', // Use current date for latest API features
      useCdn: true, // Enable CDN for better performance
      token: process.env.NEXT_PUBLIC_SANITY_TOKEN, // Read-only token for public content
    })
  }
  return sanityClient
}

export function getImageBuilder() {
  if (!imageBuilder) {
    imageBuilder = imageUrlBuilder(getSanityClient())
  }
  return imageBuilder
}

// Helper function to generate optimized image URLs
export function urlFor(source: any) {
  return getImageBuilder().image(source)
}

// Type definitions for Sanity content
export interface SanityImage {
  _type: 'image'
  asset: {
    _ref: string
    _type: 'reference'
  }
  alt?: string
}

export interface BlogPost {
  _id: string
  _type: 'blogPost'
  title: string
  slug: {
    current: string
  }
  excerpt?: string
  publishedAt: string
  author?: {
    name: string
    image?: SanityImage
  }
  mainImage?: SanityImage
  body: any[] // Portable Text content
  categories?: Array<{
    title: string
    slug: { current: string }
  }>
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
}

export interface GuidePost {
  _id: string
  _type: 'guide'
  title: string
  slug: {
    current: string
  }
  excerpt?: string
  publishedAt: string
  difficulty?: 'beginner' | 'intermediate' | 'advanced'
  estimatedReadTime?: number
  mainImage?: SanityImage
  body: any[] // Portable Text content
  tags?: Array<{
    title: string
    slug: { current: string }
  }>
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
}

// GROQ queries for fetching content
export const BLOG_POSTS_QUERY = `
  *[_type == "blogPost" && !(_id in path("drafts.**"))] | order(publishedAt desc) {
    _id,
    title,
    slug,
    excerpt,
    publishedAt,
    author->{
      name,
      image
    },
    mainImage,
    categories[]->{
      title,
      slug
    },
    seo
  }
`

export const BLOG_POST_QUERY = `
  *[_type == "blogPost" && slug.current == $slug && !(_id in path("drafts.**"))][0] {
    _id,
    title,
    slug,
    excerpt,
    publishedAt,
    author->{
      name,
      image
    },
    mainImage,
    body,
    categories[]->{
      title,
      slug
    },
    seo
  }
`

export const GUIDE_POSTS_QUERY = `
  *[_type == "guide" && !(_id in path("drafts.**"))] | order(publishedAt desc) {
    _id,
    title,
    slug,
    excerpt,
    publishedAt,
    difficulty,
    estimatedReadTime,
    mainImage,
    tags[]->{
      title,
      slug
    },
    seo
  }
`

export const GUIDE_POST_QUERY = `
  *[_type == "guide" && slug.current == $slug && !(_id in path("drafts.**"))][0] {
    _id,
    title,
    slug,
    excerpt,
    publishedAt,
    difficulty,
    estimatedReadTime,
    mainImage,
    body,
    tags[]->{
      title,
      slug
    },
    seo
  }
`

// Fetch functions with error handling and caching
export async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    const client = getSanityClient()
    return await client.fetch(BLOG_POSTS_QUERY)
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }
}

export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const client = getSanityClient()
    return await client.fetch(BLOG_POST_QUERY, { slug })
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return null
  }
}

export async function getGuidePosts(): Promise<GuidePost[]> {
  try {
    const client = getSanityClient()
    return await client.fetch(GUIDE_POSTS_QUERY)
  } catch (error) {
    console.error('Error fetching guide posts:', error)
    return []
  }
}

export async function getGuidePost(slug: string): Promise<GuidePost | null> {
  try {
    const client = getSanityClient()
    return await client.fetch(GUIDE_POST_QUERY, { slug })
  } catch (error) {
    console.error('Error fetching guide post:', error)
    return null
  }
}
