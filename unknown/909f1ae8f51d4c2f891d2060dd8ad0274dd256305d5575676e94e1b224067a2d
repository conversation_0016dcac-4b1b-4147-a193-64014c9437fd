'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { OTPInput } from './otp-input'
import { Smartphone, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react'
import { requestManualApproval } from '@/lib/actions/contact-requests'

interface PhoneVerificationProps {
  phone: string
  userId: string
  onSuccess: () => void
  onBack?: () => void
}

export function PhoneVerification({ phone, userId, onSuccess, onBack }: PhoneVerificationProps) {
  const [otp, setOtp] = useState('')
  const [isVerifying, setIsVerifying] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [canResend, setCanResend] = useState(false)
  const [countdown, setCountdown] = useState(60)
  const [verificationId, setVerificationId] = useState('')
  const [manualRequestSent, setManualRequestSent] = useState(false)

  // Ref to track if initial OTP has been sent to prevent double sending
  const otpSentRef = useRef(false)

  // Format phone for display
  const formatPhoneDisplay = (phoneNumber: string) => {
    const cleaned = phoneNumber.replace(/\D/g, '')
    if (cleaned.length === 10) {
      return `+91 ${cleaned.slice(0, 5)} ${cleaned.slice(5)}`
    }
    return phoneNumber
  }

  // Send OTP (memoized to prevent unnecessary re-renders)
  const sendOTP = useCallback(async () => {
    setIsSending(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phone, userId })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // Don't set success state here - that disables the input!
        // Just clear any previous errors and set the verification ID
        setError('')
        setVerificationId(data.verificationId) // Store verification ID from VerifyNow
        setCanResend(false)
        setCountdown(60)

        // Start countdown
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer)
              setCanResend(true)
              return 0
            }
            return prev - 1
          })
        }, 1000)
      } else {
        setError(data.error || 'Failed to send OTP')
      }
    } catch (err) {
      setError('Network error. Please check your connection.')
    } finally {
      setIsSending(false)
    }
  }, [phone, userId])

  // Verify OTP
  const verifyOTP = async (otpValue: string) => {
    if (!verificationId) {
      setError('No verification ID. Please request a new OTP.')
      return
    }

    setIsVerifying(true)
    setError('')

    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ verificationId, otp: otpValue, userId })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setSuccess('Phone verified successfully!')
        setTimeout(() => {
          onSuccess()
        }, 1500)
      } else {
        // Convert technical errors to user-friendly messages
        let userMessage = 'Invalid OTP. Please try again.'

        if (data.error) {
          const errorLower = data.error.toLowerCase()
          if (errorLower.includes('invalid') || errorLower.includes('wrong') || errorLower.includes('incorrect')) {
            userMessage = 'Incorrect OTP. Please check and try again.'
          } else if (errorLower.includes('expired') || errorLower.includes('timeout')) {
            userMessage = 'OTP has expired. Please request a new one.'
          } else if (errorLower.includes('verification_failed')) {
            userMessage = 'Verification failed. Please try again.'
          }
          // Don't show technical error codes to users
        }

        setError(userMessage)
        setOtp('') // Clear OTP on error
      }
    } catch (err) {
      setError('Network error. Please try again.')
      setOtp('')
    } finally {
      setIsVerifying(false)
    }
  }

  // Handle OTP completion
  const handleOTPComplete = (value: string) => {
    if (value.length === 4 && !isVerifying) {
      verifyOTP(value)
    }
  }

  // Handle manual approval request
  const handleManualApprovalRequest = async () => {
    setIsSending(true) // Reuse the loading state
    setError('')

    const result = await requestManualApproval(userId)
    if (result.success) {
      setManualRequestSent(true)
    } else {
      setError(result.message || 'Failed to send request. Please try again.')
    }
    setIsSending(false)
  }

  // Auto-send OTP on component mount (only once)
  useEffect(() => {
    if (!otpSentRef.current) {
      sendOTP()
      otpSentRef.current = true
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []) // Empty dependency array - sendOTP is stable and protected by ref

  return (
    <div className="mt-8 space-y-6">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mb-4">
              <Smartphone className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-slate-800 mb-2">
              Verify Your Phone
            </h2>
            <p className="text-slate-600">
              We&apos;ve sent a 4-digit code to
            </p>
            <p className="text-indigo-600 font-semibold">
              {formatPhoneDisplay(phone)}
            </p>
          </div>

          {/* Success Message */}
          {success && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl flex items-center space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
              <span className="text-green-800 text-sm">{success}</span>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
              <span className="text-red-800 text-sm">{error}</span>
            </div>
          )}

          {/* OTP Input */}
          <div className="mb-6">
            <OTPInput
              length={4}
              value={otp}
              onChange={setOtp}
              onComplete={handleOTPComplete}
              disabled={isVerifying || !!success}
              error={!!error}
            />
          </div>

          {/* Verify Button */}
          <button
            onClick={() => verifyOTP(otp)}
            disabled={otp.length !== 4 || isVerifying || !!success}
            className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 mb-4"
          >
            {isVerifying ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span>Verifying...</span>
              </>
            ) : (
              <span>Verify Phone</span>
            )}
          </button>

          {/* Resend OTP */}
          <div className="text-center">
            <p className="text-slate-600 text-sm mb-2">
              Didn&apos;t receive the code?
            </p>
            <button
              onClick={sendOTP}
              disabled={!canResend || isSending}
              className="text-indigo-600 hover:text-indigo-700 font-medium text-sm disabled:text-slate-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2 mx-auto"
            >
              {isSending ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Sending...</span>
                </>
              ) : canResend ? (
                <>
                  <RefreshCw className="w-4 h-4" />
                  <span>Resend OTP</span>
                </>
              ) : (
                <span>Resend in {countdown}s</span>
              )}
            </button>
          </div>

          {/* Manual Approval Fallback */}
          <div className="text-center mt-6 pt-6 border-t border-slate-200">
            {manualRequestSent ? (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-sm text-green-800">
                <p className="font-medium">Request Sent!</p>
                <p>Our team will review your account within 24 hours. You will be notified via email upon approval.</p>
              </div>
            ) : (
              <>
                <p className="text-slate-600 text-sm mb-2">
                  Having trouble receiving the code?
                </p>
                <button
                  onClick={handleManualApprovalRequest}
                  disabled={isSending}
                  className="text-indigo-600 hover:text-indigo-700 font-medium text-sm disabled:text-slate-400 disabled:cursor-not-allowed"
                >
                  {isSending ? 'Sending Request...' : 'Request Manual Approval'}
                </button>
              </>
            )}
          </div>

          {/* Back Button */}
          {onBack && (
            <div className="text-center mt-6">
              <button
                onClick={onBack}
                className="text-slate-500 hover:text-slate-700 text-sm"
              >
                ← Back to signup
              </button>
            </div>
          )}
        </div>
  )
}
