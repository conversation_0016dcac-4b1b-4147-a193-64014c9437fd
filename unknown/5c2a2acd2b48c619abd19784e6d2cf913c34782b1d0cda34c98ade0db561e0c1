#!/usr/bin/env node

/**
 * Fix Admin Approved Field Script
 * Usage: node database/create/fix-admin-approved-field.js
 * 
 * This script updates existing admin users to have approved = NULL
 * This prevents them from logging into the doctor portal
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  console.error('   Make sure your .env.local file is properly configured.')
  process.exit(1)
}

// Create Supabase client with service role key (bypasses RLS)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function fixAdminApprovedField() {
  try {
    console.log('🔧 Fixing Admin Approved Field\n')
    
    // Find all admin users
    const { data: adminUsers, error: fetchError } = await supabase
      .from('profiles')
      .select('id, email, name, role, approved')
      .in('role', ['admin', 'super_admin'])
    
    if (fetchError) {
      console.error('❌ Error fetching admin users:', fetchError.message)
      process.exit(1)
    }
    
    if (!adminUsers || adminUsers.length === 0) {
      console.log('✅ No admin users found.')
      process.exit(0)
    }
    
    console.log(`📊 Found ${adminUsers.length} admin user(s):`)
    adminUsers.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.name} (${admin.email}) - Role: ${admin.role} - Approved: ${admin.approved}`)
    })
    console.log('')
    
    // Update all admin users to have approved = NULL
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        approved: null // NULL = admin-only access, prevents doctor portal login
      })
      .in('role', ['admin', 'super_admin'])
    
    if (updateError) {
      console.error('❌ Error updating admin users:', updateError.message)
      process.exit(1)
    }
    
    console.log('✅ Successfully updated all admin users!')
    console.log('🔒 Admin users can now only access the admin portal.')
    console.log('🚫 Admin users are blocked from the doctor portal.')
    
  } catch (error) {
    console.error('❌ Script failed:', error.message)
    process.exit(1)
  }
}

// Run the script
fixAdminApprovedField()
