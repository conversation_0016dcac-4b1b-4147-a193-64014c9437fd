import { getAdminDashboardStats, getAllDoctorsWithStats } from '@/lib/actions/admin'
import { AdminDashboardStats } from '@/components/admin/admin-dashboard-stats'
import { DoctorsTable } from '@/components/admin/doctors-table'
import { BillingManagement } from '@/components/admin/billing-management'

interface AdminDataProps {
  activeTab: string
}

export async function AdminData({ activeTab }: AdminDataProps) {
  // This runs inside Suspense boundary, not blocking initial page render
  const [statsResult, doctorsResult] = await Promise.all([
    getAdminDashboardStats(),
    getAllDoctorsWithStats()
  ])

  const stats = statsResult.success ? statsResult.data : null
  const doctors = doctorsResult.success ? doctorsResult.data : []

  if (!stats) {
    return (
      <div className="p-6 text-center">
        <p className="text-red-600">Failed to load dashboard data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Dashboard Stats */}
      <AdminDashboardStats stats={stats} />

      {/* Tab Content */}
      {activeTab === 'doctors' && (
        <DoctorsTable doctors={doctors} />
      )}
      
      {activeTab === 'billing' && (
        <BillingManagement />
      )}
    </div>
  )
}
