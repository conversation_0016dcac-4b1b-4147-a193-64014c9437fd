-- Migration: Make approved field nullable for admin users
-- This allows us to use NULL as a security mechanism:
-- NULL = admin-only access, true = doctor access, false = pending

-- Remove the NOT NULL constraint from the approved field
ALTER TABLE public.profiles 
ALTER COLUMN approved DROP NOT NULL;

-- Update existing admin users to have approved = NULL
UPDATE public.profiles 
SET approved = NULL 
WHERE role IN ('admin', 'super_admin');

-- Add a comment explaining the new logic
COMMENT ON COLUMN public.profiles.approved IS 'NULL = admin-only access, true = doctor portal access, false = pending approval';
