# 🧠 Celer AI Backend - The AI Brain

## 🎯 **Backend Overview**

The Celer AI Python backend is a **FastAPI-based AI processing service** that serves as the intelligent core of the medical documentation system. It transforms raw audio recordings and medical images into structured, professional medical summaries using Google's Gemini 2.5 Flash Preview model.

### **Core Purpose**
- **AI Brain**: Central intelligence for processing medical consultations
- **Multi-Modal Processing**: Handles audio, images, and text simultaneously
- **Streaming Responses**: Real-time summary generation with live updates
- **Medical Specialization**: Tailored for Indian healthcare context and terminology

---

## 🏗️ **Architecture & Tech Stack**

### **Technology Foundation**
```
FastAPI + Python 3.11+ + Google Gemini 2.5 Flash
├── Async/Await (High-performance concurrent processing)
├── Streaming Responses (Real-time AI generation)
├── Multi-Modal AI (Audio + Images + Text)
├── File Processing (FFmpeg + PIL)
└── Cloud Deployment (Google Cloud Run)
```

### **System Architecture**
```
Frontend Request → FastAPI → File Processing → Gemini AI → Streaming Response
     ↓               ↓            ↓              ↓            ↓
Supabase URLs → Download → Convert → Upload → Generate → Stream Back
```

### **Project Structure**
```
python-backend/
├── main.py              # Core FastAPI application
├── prompts.json         # AI prompt configurations
├── requirements.txt     # Python dependencies
├── Dockerfile          # Container configuration
└── README.md           # Backend documentation
```

---

## 🤖 **AI Processing Pipeline**

### **Multi-Modal File Processing**
```python
# Concurrent processing of multiple file types
async def process_consultation_files(request):
    all_file_tasks = []
    
    # Audio files (primary + additional)
    all_audio_urls = [request.primary_audio_url] + (request.additional_audio_urls or [])
    for i, audio_url in enumerate(all_audio_urls):
        identifier = "primary_audio" if i == 0 else f"additional_audio_{i}"
        all_file_tasks.append(process_single_audio_file(audio_url, identifier))
    
    # Image files
    for i, image_url in enumerate(request.image_urls or []):
        all_file_tasks.append(process_single_image_file(image_url, f"image_{i}"))
    
    # Process all files concurrently
    results = await asyncio.gather(*all_file_tasks)
    return results
```

### **Audio Processing with FFmpeg**
```python
async def convert_audio_to_wav(file_bytes: bytes) -> tuple[bytes, str]:
    """Convert audio to WAV format with 16kHz mono for optimal AI processing"""
    def _blocking_ffmpeg_operations():
        # Find FFmpeg executable
        ffmpeg_path = shutil.which('ffmpeg') or '/opt/homebrew/bin/ffmpeg'
        
        # Convert to optimal format for Gemini
        cmd = [
            ffmpeg_path,
            '-i', input_file,
            '-acodec', 'pcm_s16le',  # 16-bit PCM
            '-ac', '1',              # Mono channel
            '-ar', '16000',          # 16kHz sample rate
            '-y',                    # Overwrite output
            output_file
        ]
        
        subprocess.run(cmd, capture_output=True, text=True)
        return wav_bytes, 'audio/wav'
    
    return await asyncio.to_thread(_blocking_ffmpeg_operations)
```

### **Image Processing with PIL**
```python
async def convert_image_to_png(file_bytes: bytes, max_size: int = 1024) -> tuple[bytes, str]:
    """Convert and optimize images for AI processing"""
    def _blocking_pil_operations():
        img = Image.open(io.BytesIO(file_bytes))
        
        # Convert to RGB if needed
        if img.mode in ('RGBA', 'P'):
            img = img.convert('RGB')
        
        # Resize if too large
        width, height = img.size
        if width > max_size or height > max_size:
            ratio = min(max_size/width, max_size/height)
            new_width, new_height = int(width * ratio), int(height * ratio)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Save as optimized PNG
        output = io.BytesIO()
        img.save(output, format='PNG', optimize=True)
        return output.getvalue(), 'image/png'
    
    return await asyncio.to_thread(_blocking_pil_operations)
```

---

## 🔄 **API Endpoints & Functionality**

### **Health Check Endpoint**
```python
@app.get("/health")
async def health_check():
    """System health and Gemini connectivity status"""
    client_connected = False
    try:
        temp_client = genai.Client(api_key=gemini_manager.api_key)
        client_connected = bool(temp_client)
    except Exception:
        client_connected = False
    
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "gemini_client": "connected" if client_connected else "disconnected",
        "model": "gemini-2.5-flash-preview-05-20"
    }
```

### **Streaming Summary Generation**
```python
@app.post("/api/generate-summary-stream")
async def generate_summary_stream(req: Request, request: GenerateSummaryStreamRequest):
    """Generate AI summary with real-time streaming response"""
    
    async def stream_generator():
        async with gemini_manager as client:
            # Process files concurrently
            all_results = await asyncio.gather(*all_file_processing_tasks)
            
            # Build Gemini content array
            contents = [prompt]  # Medical prompt from prompts.json
            for part, identifier, url, error in all_results:
                if part and not error:
                    contents.append(part)
            
            # Send metadata first
            metadata = {
                "type": "metadata",
                "model": "gemini-2.5-flash-lite-preview-06-17",
                "timestamp": datetime.now().isoformat(),
                "files_processed": files_processed
            }
            yield f"data: {json.dumps(metadata)}\n\n"
            
            # Generate streaming content
            stream_response = await client.aio.models.generate_content_stream(
                model="gemini-2.5-flash-lite-preview-06-17",
                contents=contents,
                config=types.GenerateContentConfig(
                    thinking_config=types.ThinkingConfig(thinking_budget=8000)
                )
            )
            
            # Stream chunks as they arrive
            async for chunk in stream_response:
                if chunk.text:
                    chunk_data = {"type": "chunk", "text": chunk.text}
                    yield f"data: {json.dumps(chunk_data)}\n\n"
                    await asyncio.sleep(0.001)  # Prevent buffering
    
    return StreamingResponse(stream_generator(), media_type="text/plain")
```

---

## 🎯 **Medical AI Prompts & Configuration**

### **Dynamic Prompt System**
```python
# Configurable prompts loaded from prompts.json
class PromptTemplate(BaseModel):
    format: str
    style: str
    ai_instruction: str
    template_structure: str
    instructions: str
    sections: List[str]

def load_prompt_config(path: str) -> Optional[Dict[str, PromptTemplate]]:
    """Load and validate prompt configuration"""
    try:
        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        validated_config = {}
        for key, value in data.items():
            validated_config[key] = PromptTemplate(**value)
        
        return validated_config
    except Exception as e:
        logger.error(f"Failed to load prompt configuration: {e}")
        return None
```

### **Medical Prompt Template**
```python
# Specialized prompt for Indian healthcare context
MEDICAL_PROMPT_TEMPLATE = """
You are an expert medical AI assistant specializing in Indian healthcare documentation.

CONSULTATION CONTEXT:
- Patient: {patient_name}
- Doctor: {doctor_name}
- Type: {consultation_type}
- Date: {created_at}

PROCESSING INSTRUCTIONS:
1. **PRIMARY SOURCES**: Process audio recording(s) and text notes with equal priority
2. Extract key medical information from all sources (audio + text)
3. **SECONDARY**: Analyze images for visual findings (prescriptions, notes, medical images)
4. Integrate information from ALL sources for complete context
5. Use appropriate medical terminology for Indian healthcare
6. **ACCURACY CHECK**: Highlight unclear medication names/dosages with **bold text**
7. Indicate incomplete or ambiguous information clearly

SUMMARY FORMAT:
- Chief Complaint
- History of Present Illness
- Clinical Findings
- Assessment/Diagnosis
- Treatment Plan
- Follow-up Instructions
"""
```

---

## ⚡ **Performance & Optimization**

### **Gemini Client Management**
```python
class GeminiClientManager:
    """Efficient Gemini client lifecycle management"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self._client = None
    
    async def __aenter__(self):
        """Async context manager for connection pooling"""
        try:
            self._client = genai.Client(api_key=self.api_key)
            return self._client
        except Exception as e:
            logger.error(f"Failed to create Gemini client: {e}")
            return None
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Cleanup resources"""
        self._client = None
```

### **Concurrent File Processing**
```python
# Process multiple files simultaneously for better performance
async def process_files_concurrently(urls: List[str]) -> List[ProcessedFile]:
    """Process multiple files concurrently to reduce latency"""
    tasks = [process_single_file(url) for url in urls]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    processed_files = []
    for result in results:
        if isinstance(result, Exception):
            logger.error(f"File processing failed: {result}")
        else:
            processed_files.append(result)
    
    return processed_files
```

### **Streaming Response Optimization**
```python
# Optimized streaming to prevent buffering
async def optimized_streaming():
    async for chunk in ai_response_stream:
        if chunk.text:
            # Immediate yield without buffering
            yield f"data: {json.dumps({'text': chunk.text})}\n\n"
            
            # Micro-sleep to prevent blocking
            await asyncio.sleep(0.001)
            
    # Final completion signal
    yield f"data: {json.dumps({'type': 'complete'})}\n\n"
```

---

## 🔧 **Configuration & Environment**

### **Environment Variables**
```env
# Core Configuration
GEMINI_API_KEY=your_gemini_api_key
FRONTEND_URL=http://localhost:3004
PORT=8080

# File Processing
MAX_FILE_SIZE_MB=50
AUDIO_SAMPLE_RATE=16000
IMAGE_MAX_SIZE=1024

# Performance
UVICORN_WORKERS=1
TIMEOUT_KEEP_ALIVE=5
```

### **FastAPI Configuration**
```python
# Optimized FastAPI setup
app = FastAPI(
    title="Celer AI API",
    description="AI-powered consultation summary system using Gemini 2.5 Flash Preview",
    version="2.0.0",
    lifespan=lifespan  # Startup/shutdown lifecycle
)

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[os.getenv("FRONTEND_URL", "http://localhost:3004")],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### **Uvicorn Server Optimization**
```python
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8080)),
        log_level="info",
        access_log=True,
        use_colors=True,
        http="h11",              # HTTP/1.1 for better streaming
        timeout_keep_alive=5     # Keep-alive for streaming
    )
```

---

## 🚀 **Deployment & Infrastructure**

### **Google Cloud Run Deployment**
```dockerfile
# Optimized Docker container
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . .

# Run with optimized settings
CMD exec uvicorn main:app --host 0.0.0.0 --port $PORT --workers 1
```

### **Cloud Run Configuration**
- **CPU**: 2 vCPU for concurrent file processing
- **Memory**: 4GB for large file handling
- **Concurrency**: 80 requests per instance
- **Timeout**: 300 seconds for complex AI processing
- **Auto-scaling**: 0-10 instances based on demand

---

## 📊 **Error Handling & Monitoring**

### **Comprehensive Error Handling**
```python
async def safe_file_processing(url: str) -> Tuple[Optional[Part], Optional[str]]:
    """Safe file processing with detailed error reporting"""
    try:
        file_bytes, mime_type = await download_file_from_url(url)
        processed_data, final_mime = await convert_file(file_bytes, mime_type)
        part = types.Part.from_bytes(data=processed_data, mime_type=final_mime)
        return part, None
    except httpx.TimeoutException:
        return None, "File download timeout - please check network connection"
    except Exception as e:
        logger.error(f"File processing failed for {url}: {e}")
        return None, f"Processing failed: {str(e)}"
```

### **Logging & Monitoring**
```python
# Structured logging for production monitoring
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Request tracking
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    logger.info(f"{request.method} {request.url} - {response.status_code} - {process_time:.3f}s")
    return response
```

---

*The AI brain that transforms medical conversations into professional documentation* 🧠✨
