# Port Configuration & Startup Guide

## Port Assignments

### Frontend (Next.js)
- **Development Port**: 3004
- **Production Port**: 3004
- **URL**: http://localhost:3004

### Backend (Python FastAPI)
- **Development Port**: 3005
- **Production Port**: 3005
- **URL**: http://localhost:3005
- **API Documentation**: http://localhost:3005/docs
- **Health Check**: http://localhost:3005/health

## Configuration Files Updated

### Frontend Configuration
- `package.json`: Updated dev and start scripts to use port 3004
- `.env.local`: Updated `NEXT_PUBLIC_API_URL=http://localhost:3005`

### Backend Configuration
- `python-backend/.env`: Updated `PORT=3005` and `FRONTEND_URL=http://localhost:3004`
- `python-backend/start.sh`: Updated console messages to reflect port 3005

## Startup Scripts

### Option 1: Start All Services (Recommended)
```bash
./start-all.sh
```
This script starts both frontend and backend services concurrently.

### Option 2: Start Services Individually

#### Start Frontend Only
```bash
./start-frontend.sh
```

#### Start Backend Only
```bash
cd python-backend
./start.sh
```

## Manual Startup Commands

### Frontend
```bash
npm install
npm run dev
```

### Backend
```bash
cd python-backend
pip3 install -r requirements.txt
python3 main.py
```

## Service URLs

After startup, access the application at:

- **Main Application**: http://localhost:3004
- **Dashboard**: http://localhost:3004/dashboard
- **Admin Login**: http://localhost:3004/admin/login
- **Mobile Interface**: http://localhost:3004/mobile
- **API Documentation**: http://localhost:3005/docs
- **Backend Health Check**: http://localhost:3005/health

## Environment Variables

### Frontend (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:3005
NEXT_PUBLIC_SUPABASE_URL=https://tzjelqzwdgidsjqhmvkr.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=...
SESSION_SECRET=...
GEMINI_API_KEY=...
```

### Backend (python-backend/.env)
```
PORT=3005
FRONTEND_URL=http://localhost:3004
GEMINI_API_KEY=...
ENVIRONMENT=development
```

## Troubleshooting

### Port Already in Use
If you get "port already in use" errors:
```bash
# Kill processes on specific ports
lsof -ti:3004 | xargs kill -9
lsof -ti:3005 | xargs kill -9
```

### CORS Issues
Ensure the backend `.env` file has the correct `FRONTEND_URL=http://localhost:3004`

### Build Errors
Run a clean build:
```bash
rm -rf .next node_modules
npm install
npm run build
```

## Production Deployment

For production, update the environment variables:
- Frontend: Update `NEXT_PUBLIC_API_URL` to your production backend URL
- Backend: Update `FRONTEND_URL` to your production frontend URL
- Backend: Set `ENVIRONMENT=production`