#!/bin/bash

# Celer AI System - Full Stack Startup Script

echo "🏥 Starting Celer AI System..."
echo "==============================================="

# Function to kill processes on script exit
cleanup() {
    echo "🛑 Shutting down services..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 0
}

# Set up trap for cleanup
trap cleanup SIGINT SIGTERM

# Start Backend
echo "🐍 Starting Python Backend on port 3005..."
cd python-backend
chmod +x start.sh
./start.sh &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Start Frontend
echo "🌐 Starting Next.js Frontend on port 3004..."
chmod +x start-frontend.sh
./start-frontend.sh &
FRONTEND_PID=$!

echo "==============================================="
echo "✅ Services started successfully!"
echo "🌍 Frontend: http://localhost:3004"
echo "🔧 Backend API: http://localhost:3005"
echo "📚 API Docs: http://localhost:3005/docs"
echo "📊 Health Check: http://localhost:3005/health"
echo "==============================================="
echo "Press Ctrl+C to stop all services"

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID