'use client'

import { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { X, Play, Pause, Wand2, Save, Copy, Camera, Upload, Mic, Square, Trash2, Loader2 } from 'lucide-react'
import { Consultation, ConsultationType, CONSULTATION_TYPE_LABELS } from '@/lib/types'
import { formatDate } from '@/lib/utils'
import { approveConsultation, addAdditionalAudio, deleteAdditionalAudio, deleteConsultationImage } from '@/lib/actions/consultations'
import { trackConsultation } from '@/lib/analytics'
import Image from 'next/image'

interface ConsultationModalProps {
  consultation: Consultation
  onClose: () => void
  onConsultationUpdate?: (updatedConsultation: Consultation) => void
  doctorName?: string
}

export function ConsultationModal({ consultation, onClose, onConsultationUpdate, doctorName }: ConsultationModalProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [isApproving, setIsApproving] = useState(false)
  const [editedNote, setEditedNote] = useState(consultation.edited_note || consultation.ai_generated_note || '')
  const [playingAudio, setPlayingAudio] = useState<HTMLAudioElement | null>(null)
  const [isLoadingAudio, setIsLoadingAudio] = useState(false)

  // Web Audio API refs for Safari
  const audioContextRef = useRef<AudioContext | null>(null)
  const audioSourceRef = useRef<AudioBufferSourceNode | null>(null)
  const [success, setSuccess] = useState<string>('')
  const [additionalImages, setAdditionalImages] = useState<Array<{ id: string; url: string; preview?: string }>>([])
  const [isRecordingAdditional, setIsRecordingAdditional] = useState(false)
  const [additionalAudioBlob, setAdditionalAudioBlob] = useState<Blob | null>(null)
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)

  const [streamingText, setStreamingText] = useState('')
  const [mounted, setMounted] = useState(false)

  // New consultation type and additional notes state
  const [consultationType, setConsultationType] = useState<ConsultationType>(
    (consultation.consultation_type as ConsultationType) || 'outpatient'
  )
  const [additionalNotes, setAdditionalNotes] = useState(consultation.additional_notes || '')

  // Delete functionality state
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null)
  const [showDeleteMenu, setShowDeleteMenu] = useState<{ type: 'audio' | 'image', url: string } | null>(null)
  
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const cameraInputRef = useRef<HTMLInputElement | null>(null)
  const successTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Function to set success message with auto-clear timeout
  const setSuccessWithTimeout = (message: string, timeout = 3000) => {
    // Clear any existing timeout
    if (successTimeoutRef.current) {
      clearTimeout(successTimeoutRef.current)
    }

    setSuccess(message)

    // Set new timeout to clear message
    if (message) {
      successTimeoutRef.current = setTimeout(() => {
        setSuccess('')
        successTimeoutRef.current = null
      }, timeout)
    }
  }

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true)
  }, [])

  // Clear success messages when consultation changes
  useEffect(() => {
    setSuccess('')
    if (successTimeoutRef.current) {
      clearTimeout(successTimeoutRef.current)
      successTimeoutRef.current = null
    }
  }, [consultation.id])

  // Cleanup long press timer on unmount
  useEffect(() => {
    return () => {
      if (longPressTimer) {
        clearTimeout(longPressTimer)
      }
    }
  }, [longPressTimer])

  // Sync editedNote state when consultation data changes
  useEffect(() => {
    setEditedNote(consultation.edited_note || consultation.ai_generated_note || '')
  }, [consultation.edited_note, consultation.ai_generated_note])

  // Sync additionalNotes state when consultation data changes
  useEffect(() => {
    setAdditionalNotes(consultation.additional_notes || '')
  }, [consultation.additional_notes])

  // Handle additional image upload - Upload to Cloudflare R2
  const handleImageUpload = async (files: FileList) => {
    try {
      setSuccess('Uploading images...')

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        if (!file.type.startsWith('image/')) continue
        if (file.size > 10 * 1024 * 1024) continue // 10MB limit

        // Upload to Cloudflare R2
        const { uploadFile } = await import('@/lib/storage')
        const uploadResult = await uploadFile(file, consultation.doctor_id || '', consultation.id, 'image')

        if (uploadResult.success && uploadResult.url) {
          const preview = URL.createObjectURL(file)
          setAdditionalImages(prev => [...prev, {
            id: `${Date.now()}-${i}`,
            url: uploadResult.url!,
            preview
          }])
        } else {
          setSuccess(`Failed to upload ${file.name}: ${uploadResult.error}`)
          return
        }
      }

      setSuccessWithTimeout('Images uploaded successfully!')
    } catch (_error) {
      setSuccess('Failed to upload images')
    }
  }

  // Remove additional image
  const removeAdditionalImage = (id: string) => {
    setAdditionalImages(prev => {
      const imageToRemove = prev.find(img => img.id === id)
      if (imageToRemove && imageToRemove.preview && typeof imageToRemove.preview === 'string') {
        URL.revokeObjectURL(imageToRemove.preview)
      }
      return prev.filter(img => img.id !== id)
    })
  }

  const handleGenerateSummary = async () => {
    setIsGenerating(true)
    setSuccess('')
    setStreamingText('')

    try {
      // Combine original images with additional images
      const allImages = [
        ...(Array.isArray(consultation.image_urls) ? consultation.image_urls : []),
        ...additionalImages.map(img => img.url)
      ].filter((img): img is string => typeof img === 'string' && img !== null)

      // Update the consultation with additional images before generating summary
      if (additionalImages.length > 0) {
        const { updateConsultationImages } = await import('@/lib/actions/consultations')
        await updateConsultationImages(consultation.id, allImages)
      }

      // Simple client-side streaming with quota checking
      try {
          const response = await fetch('/api/generate-summary-stream', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              primary_audio_url: consultation.primary_audio_url,
              additional_audio_urls: Array.isArray(consultation.additional_audio_urls) ? consultation.additional_audio_urls : [],
              image_urls: [
                ...(Array.isArray(consultation.image_urls) ? consultation.image_urls : []),
                ...allImages
              ],

              submitted_by: consultation.submitted_by || 'doctor',
              consultation_type: consultationType,
              doctor_notes: consultation.doctor_notes || undefined,
              additional_notes: additionalNotes || undefined,
              patient_name: consultation.patient_name || undefined,
              doctor_name: doctorName || undefined,
              created_at: consultation.created_at || undefined,
            }),
          })

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }

          const reader = response.body?.getReader()
          if (!reader) {
            throw new Error('No reader available')
          }

          const decoder = new TextDecoder()
          let fullSummary = ''
          setStreamingText('')

          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6))
                  if (data.type === 'chunk' && data.text) {
                    // Format text with proper line breaks
                    const formattedText = data.text.replace(/\\n/g, '\n').replace(/\n\n+/g, '\n\n')
                    fullSummary += formattedText
                    // Instantly update streaming text with accumulated content
                    setStreamingText(fullSummary)
                  }
                } catch (_e) {
                  // Ignore parse errors
                }
              }
            }
          }

          setEditedNote(fullSummary)
          
          // Save the streamed summary using the proper server action
          try {
            const { saveStreamingSummary } = await import('@/lib/actions/consultations')
            const saveResult = await saveStreamingSummary(consultation.id, fullSummary)

            if (saveResult.success) {
              setSuccessWithTimeout('Streaming summary generated and saved successfully!')

              // Track successful consultation generation (PII-free)
              trackConsultation('generated', consultationType)

              // Update the consultation object for the parent component
              if (onConsultationUpdate) {
                onConsultationUpdate({
                  ...consultation,
                  ai_generated_note: fullSummary,
                  status: 'generated' as const
                })
              }
            } else {
              console.error('Failed to save streaming summary:', saveResult.error)
              setSuccess(`Summary generated but failed to save: ${saveResult.error}`)
            }
          } catch (saveError) {
            console.error('Error saving streaming summary:', saveError)
            setSuccess('Summary generated but failed to save. Please try regenerate.')
          }
          
          setTimeout(() => setSuccess(''), 3000)
        } catch (error) {
          console.error('Streaming error:', error)
          setSuccess('Failed to generate streaming summary')
          setTimeout(() => setSuccess(''), 5000)
        }

    } catch {
      setSuccess('An unexpected error occurred')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleApprove = async () => {
    if (!editedNote.trim()) {
      setSuccess('Please provide a summary before approving')
      return
    }

    setIsApproving(true)
    setSuccess('')

    try {
      const result = await approveConsultation(consultation.id, editedNote)

      if (result.success) {
        setSuccess('Consultation approved successfully!')

        // Track successful consultation approval (PII-free)
        trackConsultation('approved', consultationType)

        setTimeout(() => {
          setSuccess('')
          onClose()
        }, 2000)
      } else {
        setSuccess(result.error || 'Failed to approve consultation')
      }
    } catch {
      setSuccess('An unexpected error occurred')
    } finally {
      setIsApproving(false)
    }
  }

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(editedNote)
      setSuccessWithTimeout('Copied to clipboard!', 2000)
    } catch {
      setSuccess('Failed to copy to clipboard')
    }
  }

  // Web Audio API playback for Safari
  const handleWebAudioPlayback = async () => {
    // Stop logic - if audio is playing, stop it
    if (audioSourceRef.current) {
      audioSourceRef.current.stop()
      audioSourceRef.current = null
      setPlayingAudio(null)
      return
    }

    setIsLoadingAudio(true)
    setSuccess('Loading audio...')

    try {
      // Create or resume AudioContext (must be in user gesture)
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
      }
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume()
      }

      const audioContext = audioContextRef.current

      // Fetch audio through proxy
      const proxyUrl = `/api/audio-proxy?url=${encodeURIComponent(consultation.primary_audio_url)}`
      const response = await fetch(proxyUrl)
      if (!response.ok) {
        throw new Error(`Audio fetch failed: ${response.statusText}`)
      }
      const audioData = await response.arrayBuffer()

      // Decode audio data (handles format conversion)
      const audioBuffer = await audioContext.decodeAudioData(audioData)

      // Create source and play
      const source = audioContext.createBufferSource()
      source.buffer = audioBuffer
      source.connect(audioContext.destination)
      source.start(0)

      // Handle playback end
      source.onended = () => {
        setPlayingAudio(null)
        audioSourceRef.current = null
      }

      audioSourceRef.current = source
      setPlayingAudio({} as HTMLAudioElement) // Just to indicate playing state
      setSuccess('')

    } catch (error) {
      console.error('Web Audio API Error:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      setSuccess(`Unable to play audio: ${errorMessage}`)
      setPlayingAudio(null)
    } finally {
      setIsLoadingAudio(false)
    }
  }

  const playAudio = async () => {
    // Detect Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)

    // For Safari, use Web Audio API
    if (isSafari) {
      await handleWebAudioPlayback()
      return
    }

    // For other browsers, use existing logic
    if (playingAudio) {
      playingAudio.pause()
      playingAudio.currentTime = 0
      setPlayingAudio(null)
      return
    }

    try {
      const audio = new Audio(consultation.primary_audio_url)
      audio.onended = () => setPlayingAudio(null)
      audio.onerror = () => {
        setPlayingAudio(null)
        setSuccess('Failed to play audio')
      }

      setPlayingAudio(audio)
      await audio.play()
    } catch (error) {
      console.error('Audio playback error:', error)
      setPlayingAudio(null)
      setSuccess('Failed to play audio')
    }
  }

  // Get the best supported audio format for this device
  const getSupportedMimeType = () => {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/mpeg'
    ]
    return types.find(type => MediaRecorder.isTypeSupported(type)) || 'audio/webm'
  }

  // Additional audio recording functions
  const startAdditionalRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mimeType = getSupportedMimeType()
      const recorder = new MediaRecorder(stream, { mimeType })
      const chunks: BlobPart[] = []

      recorder.ondataavailable = (e) => chunks.push(e.data)
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: mimeType })
        setAdditionalAudioBlob(blob)
        stream.getTracks().forEach(track => track.stop())
      }

      recorder.start()
      setMediaRecorder(recorder)
      setIsRecordingAdditional(true)
    } catch (_error) {
      setSuccess('Failed to start recording. Please check microphone permissions.')
    }
  }

  const stopAdditionalRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop()
      setIsRecordingAdditional(false)
      setMediaRecorder(null)
    }
  }

  const uploadAdditionalAudio = async () => {
    if (!additionalAudioBlob) return

    try {
      const audioFile = new File([additionalAudioBlob], `additional_audio_${Date.now()}.wav`, {
        type: 'audio/wav'
      })

      const result = await addAdditionalAudio(consultation.id, audioFile)
      if (result.success) {
        setSuccess('Additional audio uploaded successfully! Refreshing consultation data...')
        setAdditionalAudioBlob(null)

        // Fetch updated consultation data
        const { getConsultations } = await import('@/lib/actions/consultations')
        const consultationsResult = await getConsultations()

        if (consultationsResult.success && consultationsResult.data) {
          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === consultation.id)
          if (updatedConsultation && onConsultationUpdate) {
            onConsultationUpdate(updatedConsultation)
            setSuccess('Additional audio uploaded successfully! Please regenerate summary to include this audio.')
          }
        }

        setSuccessWithTimeout('')
      } else {
        setSuccess(result.error || 'Failed to upload additional audio')
      }
    } catch (_error) {
      setSuccess('Failed to upload additional audio')
    }
  }

  // Delete handlers
  const handleDeleteAudio = async (audioUrl: string) => {
    try {
      setSuccess('Deleting audio...')
      const result = await deleteAdditionalAudio(consultation.id, audioUrl)

      if (result.success) {
        setSuccessWithTimeout('Audio deleted successfully!')

        // Refresh consultation data
        const { getConsultations } = await import('@/lib/actions/consultations')
        const consultationsResult = await getConsultations()

        if (consultationsResult.success && consultationsResult.data) {
          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === consultation.id)
          if (updatedConsultation && onConsultationUpdate) {
            onConsultationUpdate(updatedConsultation)
          }
        }
      } else {
        setSuccess(result.error || 'Failed to delete audio')
      }
    } catch (_error) {
      setSuccess('Failed to delete audio')
    } finally {
      setShowDeleteMenu(null)
    }
  }

  const handleDeleteImage = async (imageUrl: string) => {
    try {
      setSuccess('Deleting image...')
      const result = await deleteConsultationImage(consultation.id, imageUrl)

      if (result.success) {
        setSuccessWithTimeout('Image deleted successfully!')

        // Refresh consultation data
        const { getConsultations } = await import('@/lib/actions/consultations')
        const consultationsResult = await getConsultations()

        if (consultationsResult.success && consultationsResult.data) {
          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === consultation.id)
          if (updatedConsultation && onConsultationUpdate) {
            onConsultationUpdate(updatedConsultation)
          }
        }
      } else {
        setSuccess(result.error || 'Failed to delete image')
      }
    } catch (_error) {
      setSuccess('Failed to delete image')
    } finally {
      setShowDeleteMenu(null)
    }
  }

  // Long press handlers for mobile
  const handleLongPressStart = (type: 'audio' | 'image', url: string) => {
    const timer = setTimeout(() => {
      setShowDeleteMenu({ type, url })
    }, 500) // 500ms long press
    setLongPressTimer(timer)
  }

  const handleLongPressEnd = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer)
      setLongPressTimer(null)
    }
  }

  const modalContent = (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50">
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-white rounded-2xl max-w-5xl w-[95%] h-[90vh] shadow-2xl flex flex-col overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 px-3 sm:px-6 py-3 sm:py-4 border-b border-orange-200/50">
          <div className="flex items-start sm:items-center justify-between gap-2">
            <div className="flex-1 min-w-0">
              <h3 className="text-lg sm:text-xl font-bold text-slate-800 truncate">
                {consultation.patient_name || `Patient #${consultation.patient_number || 'N/A'}`}
              </h3>
              <p className="text-xs sm:text-sm text-slate-600 break-words">
                {formatDate(consultation.created_at)} • {consultation.submitted_by} • {consultation.status}
              </p>
              {consultation.doctor_notes && (
                <p className="text-xs sm:text-sm text-slate-500 mt-1 italic line-clamp-2">
                  Doctor&apos;s Notes: {consultation.doctor_notes}
                </p>
              )}
            </div>
            <div className="flex flex-col sm:flex-row items-end sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 flex-shrink-0">
              {/* Consultation Type Dropdown */}
              <div className="flex flex-col items-end">
                <label className="text-xs text-slate-600 mb-1 whitespace-nowrap hidden sm:block">Consultation Type</label>
                <label className="text-xs text-slate-600 mb-1 whitespace-nowrap sm:hidden">Type</label>
                <select
                  value={consultationType}
                  onChange={(e) => setConsultationType(e.target.value as ConsultationType)}
                  className="px-2 py-1 text-xs border border-orange-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent w-24 sm:w-auto text-black"
                  disabled={consultation.status === 'approved'}
                >
                  {Object.entries(CONSULTATION_TYPE_LABELS).map(([value, label]) => (
                    <option key={value} value={value} className="text-black">
                      {label}
                    </option>
                  ))}
                </select>
              </div>
              <button
                onClick={onClose}
                className="p-1.5 sm:p-2 hover:bg-orange-100 rounded-xl transition-colors flex-shrink-0"
              >
                <X className="w-4 h-4 sm:w-5 sm:h-5 text-slate-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-3 sm:p-6 space-y-4 sm:space-y-6 flex-1 overflow-y-auto">
          {/* Audio Section - Side by Side Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            {/* Primary Audio */}
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-5 border border-orange-200">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-slate-800 flex items-center">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                  Primary Audio
                </h4>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={playAudio}
                  disabled={isLoadingAudio}
                  className="flex items-center space-x-3 px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isLoadingAudio ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : playingAudio ? (
                    <Pause className="w-5 h-5" />
                  ) : (
                    <Play className="w-5 h-5" />
                  )}
                  <span>
                    {isLoadingAudio ? 'Loading...' : playingAudio ? 'Pause Audio' : 'Play Audio'}
                  </span>
                </button>
              </div>
            </div>

            {/* Additional Audio */}
            {consultation.status !== 'approved' && (
              <div className="bg-gradient-to-r from-teal-50 to-emerald-50 rounded-xl p-3 sm:p-5 border border-teal-200">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 space-y-2 sm:space-y-0">
                  <h4 className="text-base sm:text-lg font-semibold text-slate-800 flex items-center">
                    <div className="w-2 h-2 bg-teal-500 rounded-full mr-2 sm:mr-3"></div>
                    Additional Audio
                  </h4>
                  <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full self-start">Optional</span>
                </div>
                <div className="space-y-3">
                  <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 gap-2">
                    {!isRecordingAdditional && !additionalAudioBlob && (
                      <button
                        onClick={startAdditionalRecording}
                        className="flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg text-sm font-medium transition-all duration-200 w-full sm:w-auto"
                      >
                        <Mic className="w-4 h-4" />
                        <span>Record</span>
                      </button>
                    )}

                    {isRecordingAdditional && (
                      <button
                        onClick={stopAdditionalRecording}
                        className="flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg text-sm font-medium animate-pulse transition-all duration-200 w-full sm:w-auto"
                      >
                        <Square className="w-4 h-4" />
                        <span>Stop Recording</span>
                      </button>
                    )}

                    {additionalAudioBlob && (
                      <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                        <button
                          onClick={uploadAdditionalAudio}
                          className="flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm font-medium transition-all duration-200"
                        >
                          <Upload className="w-4 h-4" />
                          <span>Upload</span>
                        </button>
                        <button
                          onClick={() => setAdditionalAudioBlob(null)}
                          className="flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-all duration-200"
                        >
                          <X className="w-4 h-4" />
                          <span>Cancel</span>
                        </button>
                      </div>
                    )}
                  </div>

                  {isRecordingAdditional && (
                    <div className="flex items-center space-x-2 bg-red-100 px-3 py-2 rounded-lg">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-sm text-red-700 font-medium">Recording...</span>
                    </div>
                  )}

                  {additionalAudioBlob && (
                    <div className="flex items-center space-x-2 bg-green-100 px-3 py-2 rounded-lg">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-green-700 font-medium">Ready to upload</span>
                    </div>
                  )}

                  {/* Show existing additional audio files */}
                  {consultation.additional_audio_urls &&
                   Array.isArray(consultation.additional_audio_urls) &&
                   consultation.additional_audio_urls.length > 0 && (
                    <div className="p-3 bg-white rounded-lg border">
                      <p className="text-sm text-gray-600 mb-2">Additional Audio Files ({consultation.additional_audio_urls.length})</p>
                      <div className="space-y-2">
                        {consultation.additional_audio_urls
                          .filter((audioUrl): audioUrl is string => typeof audioUrl === 'string' && audioUrl !== null)
                          .map((audioUrl, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-gray-50 rounded border group"
                            onMouseEnter={() => consultation.status !== 'approved' && setShowDeleteMenu({ type: 'audio', url: audioUrl })}
                            onMouseLeave={() => setShowDeleteMenu(null)}
                            onTouchStart={() => consultation.status !== 'approved' && handleLongPressStart('audio', audioUrl)}
                            onTouchEnd={handleLongPressEnd}
                            onTouchCancel={handleLongPressEnd}
                          >
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                              <span className="text-sm text-gray-700">Audio {index + 1}</span>
                            </div>

                            {/* Delete button - Desktop hover / Mobile long press */}
                            {consultation.status !== 'approved' && showDeleteMenu?.type === 'audio' && showDeleteMenu?.url === audioUrl && (
                              <button
                                onClick={() => handleDeleteAudio(audioUrl)}
                                className="flex items-center space-x-1 px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-xs transition-all duration-200"
                                title="Delete audio"
                              >
                                <Trash2 className="w-3 h-3" />
                                <span>Delete</span>
                              </button>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Images Section - Compact Layout */}
          {consultation.image_urls && Array.isArray(consultation.image_urls) && consultation.image_urls.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                Original Images ({consultation.image_urls.length} image{consultation.image_urls.length > 1 ? 's' : ''})
              </h4>
              <div className="flex flex-wrap gap-3">
                {consultation.image_urls
                  .filter((imageUrl): imageUrl is string => typeof imageUrl === 'string' && imageUrl !== null)
                  .map((imageUrl, index) => (
                    <div
                      key={index}
                      className="relative group"
                      onMouseEnter={() => consultation.status !== 'approved' && setShowDeleteMenu({ type: 'image', url: imageUrl })}
                      onMouseLeave={() => setShowDeleteMenu(null)}
                      onTouchStart={() => consultation.status !== 'approved' && handleLongPressStart('image', imageUrl)}
                      onTouchEnd={handleLongPressEnd}
                      onTouchCancel={handleLongPressEnd}
                    >
                      <div className="w-24 h-24 bg-gray-200 rounded border overflow-hidden">
                        <Image
                          src={imageUrl}
                          alt={`Original image ${index + 1}`}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-200 cursor-pointer"
                          width={96}
                          height={96}
                          priority={index < 2}
                          loading={index < 2 ? 'eager' : 'lazy'}
                          onError={(e) => {
                            console.error('Image failed to load:', imageUrl)
                            const target = e.currentTarget as HTMLImageElement
                            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTYiIGhlaWdodD0iOTYiIHZpZXdCb3g9IjAgMCA5NiA5NiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9Ijk2IiBoZWlnaHQ9Ijk2IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik00OCA2NEw0MCA1Nkw0OCA0OEw1NiA1Nkw0OCA2NFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
                            target.alt = 'Failed to load image'
                          }}
                        />
                      </div>
                      <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 py-0.5 rounded">
                        {index + 1}
                      </div>

                      {/* Delete button - Desktop hover / Mobile long press */}
                      {consultation.status !== 'approved' && showDeleteMenu?.type === 'image' && showDeleteMenu?.url === imageUrl && (
                        <button
                          onClick={() => handleDeleteImage(imageUrl)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs shadow-lg transition-all duration-200 z-10"
                          title="Delete image"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          )}

          {/* Additional Images Section for Nurses */}
          <div className="bg-orange-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-slate-800">
                Additional Images
              </h4>
              <span className="text-xs text-slate-500">Optional</span>
            </div>

            <div className="flex items-center space-x-3 mb-3">
              {/* Compact Camera button */}
              <button
                onClick={() => cameraInputRef.current?.click()}
                className="flex items-center space-x-2 px-3 py-2 border border-orange-300 rounded-md hover:bg-orange-100 transition-colors text-sm"
              >
                <Camera className="w-4 h-4 text-orange-500" />
                <span className="text-orange-700">Camera</span>
              </button>

              {/* Compact Upload button */}
              <button
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center space-x-2 px-3 py-2 border border-orange-300 rounded-md hover:bg-orange-100 transition-colors text-sm"
              >
                <Upload className="w-4 h-4" />
                <span className="text-orange-700">Upload</span>
              </button>

              {additionalImages.length > 0 && (
                <span className="text-xs text-orange-600 font-medium">
                  +{additionalImages.length} image{additionalImages.length > 1 ? 's' : ''}
                </span>
              )}
            </div>

            {/* Compact image previews */}
            {additionalImages.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {additionalImages.map((image) => (
                  <div key={image.id} className="relative">
                    <Image
                      src={image.preview || ''}
                      alt="Additional"
                      className="w-12 h-12 object-cover rounded border hover:w-24 hover:h-24 transition-all duration-200 cursor-pointer"
                      width={48}
                      height={48}
                    />
                    <button
                      onClick={() => removeAdditionalImage(image.id)}
                      className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs"
                    >
                      <X className="w-2.5 h-2.5" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Hidden inputs */}
            <input
              ref={cameraInputRef}
              type="file"
              accept="image/*"
              capture="environment"
              onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
              className="hidden"
            />
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
              className="hidden"
            />
          </div>

          {/* Additional Notes Section */}
          <div className="bg-blue-50 rounded-xl p-3 sm:p-5 border border-blue-100">
            <h4 className="text-base sm:text-lg font-semibold text-slate-800 mb-3 sm:mb-4 flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 sm:mr-3"></div>
              Additional Notes (Optional)
            </h4>
            <textarea
              value={additionalNotes}
              onChange={(e) => setAdditionalNotes(e.target.value)}
              placeholder="Add any additional context or instructions for the AI..."
              rows={3}
              className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm bg-white"
              disabled={consultation.status === 'approved'}
            />
            <p className="text-xs text-blue-600 mt-2">
              These notes will be included in the AI analysis along with the audio recordings.
            </p>
          </div>

          {/* Summary Section */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-100">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                AI-Generated Patient Summary
              </h4>
              <div className="flex flex-wrap items-center gap-2">
                {(consultation.status === 'pending_generation' || consultation.status === 'generated') && (
                  <button
                    onClick={handleGenerateSummary}
                    disabled={isGenerating}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:from-purple-300 disabled:to-purple-400 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    <Wand2 className="w-4 h-4" />
                    <span className="hidden sm:inline">{isGenerating ? 'Generating...' : consultation.status === 'generated' ? 'Regenerate Summary' : 'Generate Summary'}</span>
                    <span className="sm:hidden">{isGenerating ? 'Gen...' : 'Generate'}</span>
                  </button>
                )}

                {editedNote && (
                  <button
                    onClick={handleCopyToClipboard}
                    className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    <Copy className="w-4 h-4" />
                    <span>Copy</span>
                  </button>
                )}
              </div>
            </div>

            <textarea
              value={isGenerating ? streamingText : editedNote}
              onChange={(e) => setEditedNote(e.target.value)}
              placeholder={
                consultation.status === 'pending_generation'
                  ? 'Click "Generate Summary" to create an AI-powered patient summary ...'
                  : 'Edit the patient summary as needed...'
              }
              className="w-full min-h-[400px] max-h-[600px] p-4 border border-gray-200 rounded-lg resize-y focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white text-black text-sm font-medium flex-1"
              style={{
                height: (isGenerating ? streamingText : editedNote) ? `${Math.max(400, Math.min(600, (isGenerating ? streamingText : editedNote).split('\n').length * 24 + 80))}px` : '400px'
              }}
            />
            {isGenerating && (
              <div className="text-xs text-purple-600 mt-2 flex items-center">
                <span className="inline-block w-2 h-2 bg-purple-500 animate-pulse mr-2 rounded-full"></span>
                Streaming response...
              </div>
            )}
          </div>

          {/* Status Messages */}
          {success && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 shadow-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <p className="text-sm text-green-700 font-medium">{success}</p>
              </div>
            </div>
          )}
        </div>

        {/* Actions Footer */}
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 px-4 sm:px-6 py-4 border-t border-orange-200/50 flex-shrink-0">
          <div className="flex flex-col space-y-4">
            {/* Status Info */}
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
              <div className={`px-3 py-1 rounded-full text-xs font-medium inline-flex items-center w-fit ${
                consultation.status === 'pending_generation' ? 'bg-orange-100 text-orange-800 border border-orange-300' :
                consultation.status === 'generated' ? 'bg-emerald-100 text-emerald-800 border border-emerald-300' :
                'bg-green-100 text-green-800 border border-green-300'
              }`}>
                {consultation.status.toUpperCase()}
              </div>
              <span className="text-xs sm:text-sm text-slate-600">
                Last updated: {formatDate(consultation.updated_at)}
              </span>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
              <button
                onClick={onClose}
                className="w-full sm:w-auto px-6 py-3 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-xl text-slate-700 bg-white hover:bg-orange-50 transition-all duration-200 text-center"
              >
                Close
              </button>

              {consultation.status !== 'approved' && editedNote.trim() && (
                <button
                  onClick={handleApprove}
                  disabled={isApproving}
                  className="w-full sm:w-auto flex items-center justify-center space-x-2 px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 disabled:from-teal-300 disabled:to-emerald-400 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Save className="w-4 h-4" />
                  <span>{isApproving ? 'Approving...' : 'Approve & Save'}</span>
                </button>
              )}

              {consultation.status !== 'approved' && !editedNote.trim() && (
                <div className="w-full sm:w-auto px-6 py-3 text-sm text-center text-gray-500 bg-gray-100 rounded-xl border border-gray-200">
                  Generate or add summary to approve
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  // Don't render anything on server-side
  if (!mounted) {
    return null
  }

  // Use portal to render modal at document body level
  return createPortal(modalContent, document.body)
}
