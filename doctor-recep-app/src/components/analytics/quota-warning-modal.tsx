'use client'

import { X, AlertTriangle, Phone, MessageCircle, Clock, CheckCircle } from 'lucide-react'
import { QuotaInfo } from '@/lib/types'

interface QuotaWarningModalProps {
  isOpen: boolean
  onClose: () => void
  quotaInfo: QuotaInfo
  onContactFounder: () => void
  isRequesting?: boolean
  hasRequested?: boolean
}

export function QuotaWarningModal({ 
  isOpen, 
  onClose, 
  quotaInfo, 
  onContactFounder, 
  isRequesting = false,
  hasRequested = false 
}: QuotaWarningModalProps) {
  if (!isOpen) return null

  const isOverLimit = quotaInfo.quota_percentage >= 95

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className={`p-6 text-white relative ${
          isOverLimit 
            ? 'bg-gradient-to-r from-red-500 to-red-600' 
            : 'bg-gradient-to-r from-orange-500 to-amber-600'
        }`}>
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white hover:text-gray-200 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold mb-2">
              {isOverLimit ? 'Quota Limit Reached!' : 'Quota Warning'}
            </h2>
            <p className="text-orange-100">
              {isOverLimit 
                ? 'You have reached your consultation limit' 
                : 'You are approaching your consultation limit'
              }
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Quota Status */}
          <div className="bg-gradient-to-r from-gray-50 to-slate-50 p-4 rounded-lg border border-gray-200 mb-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {quotaInfo.quota_used} / {quotaInfo.monthly_quota}
              </div>
              <div className="text-sm text-gray-600 mb-3">Consultations Used</div>
              
              <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${
                    isOverLimit ? 'bg-red-500' : 'bg-orange-500'
                  }`}
                  style={{ width: `${Math.min(quotaInfo.quota_percentage, 100)}%` }}
                />
              </div>
              
              <div className={`text-sm font-medium ${
                isOverLimit ? 'text-red-600' : 'text-orange-600'
              }`}>
                {quotaInfo.quota_percentage.toFixed(0)}% Used
              </div>
            </div>
          </div>

          {/* Message */}
          <div className="text-center mb-6">
            <p className="text-gray-700 leading-relaxed mb-4">
              {isOverLimit 
                ? 'To continue using AI consultations, please contact our founder to upgrade your plan or increase your quota.'
                : 'You have only a few consultations remaining. Consider contacting our founder to discuss upgrading your plan.'
              }
            </p>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Phone className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Contact Founder</span>
              </div>
              <div className="text-lg font-bold text-blue-900 mb-1">
                +91 8921628177
              </div>
              <p className="text-xs text-blue-700">
                Available Mon-Sat, 9 AM - 8 PM IST
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {hasRequested ? (
              <div className="flex items-center justify-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-green-800 font-medium">Request Sent Successfully!</span>
              </div>
            ) : (
              <button
                onClick={onContactFounder}
                disabled={isRequesting}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center space-x-2"
              >
                {isRequesting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Sending Request...</span>
                  </>
                ) : (
                  <>
                    <MessageCircle className="w-4 h-4" />
                    <span>Request Callback</span>
                  </>
                )}
              </button>
            )}

            <a
              href="tel:+918921628177"
              className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center space-x-2"
            >
              <Phone className="w-4 h-4" />
              <span>Call Now</span>
            </a>

            {!isOverLimit && (
              <button
                onClick={onClose}
                className="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-4 rounded-lg transition-all duration-200"
              >
                Continue with Trial
              </button>
            )}
          </div>

          {/* Reset Info */}
          <div className="mt-6 p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Clock className="w-4 h-4" />
              <span>
                Quota resets in {quotaInfo.days_until_reset} day{quotaInfo.days_until_reset !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}