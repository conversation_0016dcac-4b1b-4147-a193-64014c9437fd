import { FileText, Clock, CheckCircle, Calendar } from 'lucide-react'
import { DashboardStats as StatsType } from '@/lib/types'

interface DashboardStatsProps {
  stats: StatsType
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  const statItems = [
    {
      name: 'Total Consultations',
      value: stats.total_consultations,
      icon: FileText,
      color: 'text-teal-600',
      bgColor: 'bg-teal-100',
    },
    {
      name: 'Pending Review',
      value: stats.pending_consultations,
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
    {
      name: 'Approved',
      value: stats.approved_consultations,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: 'Today',
      value: stats.today_consultations,
      icon: Calendar,
      color: 'text-amber-600',
      bgColor: 'bg-amber-100',
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 h-full">
      {statItems.map((item) => {
        const Icon = item.icon
        return (
          <div
            key={item.name}
            className="bg-white/80 backdrop-blur-sm overflow-hidden shadow-lg rounded-lg border border-orange-200/50 hover:shadow-xl transition-all duration-300 hover:scale-105 h-full flex flex-col"
          >
            <div className="p-4 flex-1 flex flex-col justify-center items-center text-center">
              <div className="flex flex-col items-center space-y-2">
                <div className="flex-shrink-0">
                  <div className={`w-6 h-6 ${item.bgColor} rounded-lg flex items-center justify-center shadow-md`}>
                    <Icon className={`w-4 h-4 ${item.color}`} />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <dl>
                    <dt className="text-sm font-medium text-slate-800 leading-tight mb-1">
                      {item.name}
                    </dt>
                    <dd className="text-xl font-bold text-slate-800">
                      {item.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}