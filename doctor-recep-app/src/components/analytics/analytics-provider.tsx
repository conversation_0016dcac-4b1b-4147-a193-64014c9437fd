'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { initializeAnalytics, trackPageView } from '@/lib/analytics'

/**
 * Analytics Provider Component
 * Handles client-side analytics initialization and page tracking
 * Respects the two-zone security model
 * Uses lazy initialization to avoid blocking page load
 */
export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  useEffect(() => {
    // Lazy initialize analytics after page is fully loaded and idle
    const timer = setTimeout(() => {
      // Use requestIdleCallback for better performance
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => initializeAnalytics(), { timeout: 2000 })
      } else {
        initializeAnalytics()
      }
    }, 500) // Increased delay to avoid blocking initial render

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Lazy track page changes with debouncing and idle callback
    const timer = setTimeout(() => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => trackPageView(document.title), { timeout: 1000 })
      } else {
        trackPageView(document.title)
      }
    }, 300) // Increased delay to ensure GA4 is loaded

    return () => clearTimeout(timer)
  }, [pathname])

  return <>{children}</>
}
