'use client'

import Link from 'next/link'
import Image from 'next/image'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>R<PERSON>, Star, Sparkles } from 'lucide-react'
import { urlFor } from '@/lib/sanity/client'

interface GuideCardProps {
  guide: {
    _id: string
    title: string
    slug: { current: string }
    excerpt?: string
    publishedAt: string
    difficulty?: 'beginner' | 'intermediate' | 'advanced'
    estimatedReadTime?: number
    mainImage?: any
    tags?: Array<{
      title: string
      slug: { current: string }
    }>
  }
}

export function GuideCard({ guide }: GuideCardProps) {
  const publishedDate = new Date(guide.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const difficultyColors = {
    beginner: 'from-green-100 to-emerald-100 text-green-700 border-green-200',
    intermediate: 'from-yellow-100 to-orange-100 text-orange-700 border-orange-200',
    advanced: 'from-red-100 to-pink-100 text-red-700 border-red-200'
  }

  // Analytics tracking with proper error handling
  const handleClick = () => {
    if (typeof window !== 'undefined') {
      import('@/lib/analytics').then(({ trackEvent }) => {
        trackEvent('guide_viewed', { page_title: guide.title })
      }).catch(error => {
        console.warn('Analytics tracking failed:', error)
      })
    }
  }

  return (
    <article className="relative group">
      {/* Magical glow effect */}
      <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-0 group-hover:opacity-30 transition-all duration-500"></div>

      <div className="relative bg-white/90 backdrop-blur-xl rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden border border-white/20">
        {/* Featured Image */}
        {guide.mainImage && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={urlFor(guide.mainImage).width(400).height(300).fit('crop').auto('format').url()}
              alt={guide.mainImage.alt || guide.title}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-500"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />

            {/* Difficulty Badge */}
            {guide.difficulty && (
              <div className="absolute top-4 left-4">
                <span className={`px-3 py-1 bg-gradient-to-r ${difficultyColors[guide.difficulty]} text-xs font-medium rounded-full border backdrop-blur-sm`}>
                  {guide.difficulty}
                </span>
              </div>
            )}

            {/* Magical sparkle effect */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Sparkles className="w-5 h-5 text-white animate-pulse" />
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6">
          {/* Tags */}
          {guide.tags && guide.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {guide.tags.slice(0, 2).map((tag) => (
                <span
                  key={tag.slug.current}
                  className="px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-xs font-medium rounded-full border border-indigo-200"
                >
                  {tag.title}
                </span>
              ))}
            </div>
          )}

          {/* Title */}
          <h2 className="text-xl font-bold text-slate-900 mb-3 line-clamp-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-indigo-600 group-hover:to-purple-600 transition-all duration-300">
            {guide.title}
          </h2>

          {/* Excerpt */}
          {guide.excerpt && (
            <p className="text-slate-600 mb-6 line-clamp-3 leading-relaxed">
              {guide.excerpt}
            </p>
          )}

          {/* Meta */}
          <div className="flex items-center justify-between text-sm text-slate-500 mb-6">
            <div className="flex items-center space-x-4">
              {guide.estimatedReadTime && (
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{guide.estimatedReadTime} min read</span>
                </div>
              )}
              <div className="flex items-center space-x-1">
                <BookOpen className="w-4 h-4" />
                <span>{publishedDate}</span>
              </div>
            </div>
          </div>

          {/* Read Guide Link */}
          <Link
            href={`/guide/${guide.slug.current}`}
            onClick={handleClick}
            className="group/link inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 hover:from-indigo-700 hover:via-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
          >
            <span>Start Learning</span>
            <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300" />
          </Link>
        </div>
      </div>
    </article>
  )
}