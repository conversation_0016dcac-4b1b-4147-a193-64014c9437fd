'use client'

import { Plus, FileText, Clock } from 'lucide-react'
import { formatRelativeTime } from '@/lib/utils'

interface Template {
  id: string
  name: string
  content: string
  created_at: string
  updated_at: string
}

interface TemplatesSidebarProps {
  templates: Template[]
  selectedTemplate: Template | null
  onSelectTemplate: (template: Template) => void
  onNewTemplate: () => void
}

export function TemplatesSidebar({ 
  templates, 
  selectedTemplate, 
  onSelectTemplate, 
  onNewTemplate 
}: TemplatesSidebarProps) {
  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-orange-200/50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-slate-800">Templates</h2>
          <button
            onClick={onNewTemplate}
            className="flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg"
          >
            <Plus className="w-4 h-4" />
            <span className="hidden sm:inline">New</span>
          </button>
        </div>
        
        <div className="text-xs text-slate-600">
          {templates.length} template{templates.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Templates List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {templates.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-slate-400" />
            <h3 className="mt-2 text-sm font-medium text-slate-800">No templates</h3>
            <p className="mt-1 text-sm text-slate-600">
              Get started by creating your first template.
            </p>
          </div>
        ) : (
          templates.map((template) => (
            <div
              key={template.id}
              onClick={() => onSelectTemplate(template)}
              className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md ${
                selectedTemplate?.id === template.id
                  ? 'bg-gradient-to-r from-teal-50 to-emerald-50 border-teal-200 shadow-md'
                  : 'bg-white/70 border-orange-200/50 hover:bg-orange-50/50'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-slate-800 truncate">
                    {template.name}
                  </h3>
                  <div className="mt-1 flex items-center space-x-2 text-xs text-slate-600">
                    <Clock className="w-3 h-3" />
                    <span>{formatRelativeTime(template.updated_at)}</span>
                  </div>
                  <p className="mt-2 text-xs text-slate-500 line-clamp-2">
                    {template.content.substring(0, 80)}...
                  </p>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-orange-200/50">
        <div className="text-xs text-slate-500 text-center">
          Templates help standardize your consultation notes
        </div>
      </div>
    </div>
  )
}
