'use client'

import { useState, useEffect } from 'react'
import { Save, Copy, Edit3, FileText } from 'lucide-react'
import { formatDate } from '@/lib/utils'

interface Template {
  id: string
  name: string
  content: string
  created_at: string
  updated_at: string
}

interface TemplateEditorProps {
  template: Template | null
  onSave: (template: Template) => void
}

export function TemplateEditor({ template, onSave }: TemplateEditorProps) {
  const [templateName, setTemplateName] = useState('')
  const [templateContent, setTemplateContent] = useState('')
  const [isEditing, setIsEditing] = useState(false)

  useEffect(() => {
    if (template) {
      setTemplateName(template.name)
      setTemplateContent(template.content)
      setIsEditing(false)
    }
  }, [template])

  const handleSave = () => {
    if (!template) return
    
    const updatedTemplate: Template = {
      ...template,
      name: templateName,
      content: templateContent,
      updated_at: new Date().toISOString()
    }
    
    onSave(updatedTemplate)
    setIsEditing(false)
  }

  const handleCopy = async () => {
    if (templateContent) {
      try {
        await navigator.clipboard.writeText(templateContent)
        // Could add a toast notification here
      } catch (error) {
        console.error('Failed to copy template content:', error)
      }
    }
  }

  if (!template) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex items-center justify-center">
        <div className="text-center">
          <FileText className="mx-auto h-12 w-12 text-slate-400" />
          <h3 className="mt-2 text-sm font-medium text-slate-800">No template selected</h3>
          <p className="mt-1 text-sm text-slate-600">
            Select a template from the sidebar to view and edit it.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-orange-200/50">
        <div className="flex items-center justify-between mb-4">
          <div className="flex-1 min-w-0">
            {isEditing ? (
              <input
                type="text"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                className="text-lg font-semibold text-slate-800 bg-transparent border-b border-teal-300 focus:outline-none focus:border-teal-500 w-full"
                placeholder="Template name..."
              />
            ) : (
              <h2 className="text-lg font-semibold text-slate-800 truncate">
                {template.name}
              </h2>
            )}
            <div className="mt-1 text-xs text-slate-600">
              Created: {formatDate(template.created_at)} • 
              Last updated: {formatDate(template.updated_at)}
            </div>
          </div>
          
          <div className="flex items-center space-x-2 ml-4">
            <button
              onClick={handleCopy}
              className="flex items-center space-x-2 px-3 py-2 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-lg text-slate-700 bg-white/70 hover:bg-orange-50 transition-all duration-200"
            >
              <Copy className="w-4 h-4" />
              <span className="hidden sm:inline">Copy</span>
            </button>
            
            {isEditing ? (
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <Save className="w-4 h-4" />
                <span className="hidden sm:inline">Save</span>
              </button>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <Edit3 className="w-4 h-4" />
                <span className="hidden sm:inline">Edit</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Content Editor */}
      <div className="flex-1 p-4">
        <div className="h-full">
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Template Content
          </label>
          <textarea
            value={templateContent}
            onChange={(e) => setTemplateContent(e.target.value)}
            placeholder="Enter your template content here..."
            className="w-full h-full p-4 border border-orange-200 rounded-lg resize-none focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white text-slate-800 text-sm font-mono"
            readOnly={!isEditing}
            style={{
              minHeight: '400px'
            }}
          />
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-orange-200/50">
        <div className="flex items-center justify-between text-xs text-slate-500">
          <span>
            {templateContent.length} characters • {templateContent.split('\n').length} lines
          </span>
          {isEditing && (
            <span className="text-teal-600 font-medium">
              Editing mode - Click Save to apply changes
            </span>
          )}
        </div>
      </div>
    </div>
  )
}
