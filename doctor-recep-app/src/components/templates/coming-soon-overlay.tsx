'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Zap } from 'lucide-react'

export function ComingSoonOverlay() {
  const [isVisible, setIsVisible] = useState(true)

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-100 to-emerald-100 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-orange-100 to-amber-100 rounded-full translate-y-12 -translate-x-12 opacity-50"></div>
        
        {/* Close button */}
        <button
          onClick={() => setIsVisible(false)}
          className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <X className="w-5 h-5 text-gray-500" />
        </button>

        {/* Content */}
        <div className="relative z-10">
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-teal-500 to-emerald-600 rounded-full mb-4">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Coming Soon!
            </h2>
            <p className="text-gray-600">
              Custom templates feature is under development
            </p>
          </div>

          <div className="space-y-4 mb-6">
            <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-teal-50 to-emerald-50 rounded-lg">
              <div className="flex-shrink-0">
                <Zap className="w-5 h-5 text-teal-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Smart Templates</h3>
                <p className="text-xs text-gray-600">AI-powered template suggestions</p>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg">
              <div className="flex-shrink-0">
                <Clock className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Quick Access</h3>
                <p className="text-xs text-gray-600">One-click template application</p>
              </div>
            </div>
          </div>

          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 text-white rounded-lg text-sm font-medium">
              <Sparkles className="w-4 h-4 mr-2" />
              Expected: Q1 2025
            </div>
          </div>

          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              Click the X button to close this overlay
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
