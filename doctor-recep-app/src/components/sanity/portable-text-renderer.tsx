import { PortableText, PortableTextComponents } from '@portabletext/react'
import Image from 'next/image'
import Link from 'next/link'
import { urlFor, SanityImage } from '@/lib/sanity/client'

/**
 * Portable Text Renderer - Celer AI
 * 
 * SECURITY FEATURES:
 * - XSS Protection: All content sanitized through PortableText
 * - Safe HTML: No dangerouslySetInnerHTML used
 * - Link Validation: External links open in new tab with security attributes
 * - Image Optimization: Next.js Image component with Sanity CDN
 * 
 * PERFORMANCE FEATURES:
 * - Lazy image loading
 * - Optimized image sizes
 * - CDN delivery
 */

interface PortableTextRendererProps {
  content: any[]
  className?: string
}

// Custom components for rendering Portable Text blocks
const components: PortableTextComponents = {
  // Block-level elements
  block: {
    // Headings
    h1: ({ children }) => (
      <h1 className="text-4xl font-bold text-slate-900 mb-6 leading-tight">
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-3xl font-bold text-slate-900 mb-5 mt-8 leading-tight">
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-2xl font-semibold text-slate-900 mb-4 mt-6 leading-tight">
        {children}
      </h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-xl font-semibold text-slate-900 mb-3 mt-5 leading-tight">
        {children}
      </h4>
    ),
    
    // Paragraphs
    normal: ({ children }) => (
      <p className="text-slate-700 mb-4 leading-relaxed text-base">
        {children}
      </p>
    ),
    
    // Blockquotes
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-indigo-500 pl-6 py-2 my-6 bg-indigo-50 rounded-r-lg">
        <div className="text-slate-700 italic text-lg leading-relaxed">
          {children}
        </div>
      </blockquote>
    ),
  },

  // List elements
  list: {
    bullet: ({ children }) => (
      <ul className="list-disc list-inside mb-4 space-y-2 text-slate-700 ml-4">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="list-decimal list-inside mb-4 space-y-2 text-slate-700 ml-4">
        {children}
      </ol>
    ),
  },

  listItem: {
    bullet: ({ children }) => (
      <li className="leading-relaxed">{children}</li>
    ),
    number: ({ children }) => (
      <li className="leading-relaxed">{children}</li>
    ),
  },

  // Inline elements
  marks: {
    // Text formatting
    strong: ({ children }) => (
      <strong className="font-semibold text-slate-900">{children}</strong>
    ),
    em: ({ children }) => (
      <em className="italic text-slate-700">{children}</em>
    ),
    code: ({ children }) => (
      <code className="bg-slate-100 text-slate-800 px-2 py-1 rounded text-sm font-mono">
        {children}
      </code>
    ),
    
    // Links with security attributes
    link: ({ children, value }) => {
      const isExternal = value?.href?.startsWith('http')
      
      if (isExternal) {
        return (
          <a
            href={value.href}
            target="_blank"
            rel="noopener noreferrer"
            className="text-indigo-600 hover:text-indigo-800 underline transition-colors duration-200"
          >
            {children}
          </a>
        )
      }
      
      return (
        <Link
          href={value.href || '#'}
          className="text-indigo-600 hover:text-indigo-800 underline transition-colors duration-200"
        >
          {children}
        </Link>
      )
    },
  },

  // Custom types
  types: {
    // Image blocks with optimization
    image: ({ value }: { value: SanityImage & { caption?: string } }) => {
      if (!value?.asset) return null

      return (
        <figure className="my-8">
          <div className="relative rounded-lg overflow-hidden shadow-lg">
            <Image
              src={urlFor(value).width(800).height(600).fit('max').auto('format').url()}
              alt={value.alt || value.caption || 'Blog image'}
              width={800}
              height={600}
              className="w-full h-auto"
              loading="lazy"
              placeholder="blur"
              blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
            />
          </div>
          {value.caption && (
            <figcaption className="text-center text-sm text-slate-600 mt-3 italic">
              {value.caption}
            </figcaption>
          )}
        </figure>
      )
    },

    // Code blocks
    code: ({ value }: { value: { code: string; language?: string } }) => (
      <div className="my-6">
        <pre className="bg-slate-900 text-slate-100 p-4 rounded-lg overflow-x-auto">
          <code className="text-sm font-mono leading-relaxed">
            {value.code}
          </code>
        </pre>
      </div>
    ),

    // Call-to-action blocks
    callToAction: ({ value }: { value: { title: string; description: string; buttonText: string; buttonUrl: string } }) => (
      <div className="my-8 p-6 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl">
        <h3 className="text-xl font-semibold text-slate-900 mb-2">
          {value.title}
        </h3>
        <p className="text-slate-700 mb-4">
          {value.description}
        </p>
        <Link
          href={value.buttonUrl || '#'}
          className="inline-block bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105"
        >
          {value.buttonText}
        </Link>
      </div>
    ),
  },
}

export function PortableTextRenderer({ content, className = '' }: PortableTextRendererProps) {
  if (!content || !Array.isArray(content)) {
    return null
  }

  return (
    <div className={`prose prose-slate max-w-none ${className}`}>
      <PortableText
        value={content}
        components={components}
      />
    </div>
  )
}
