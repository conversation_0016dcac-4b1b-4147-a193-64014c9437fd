'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Menu, Moon, Sun, User, Settings, Info, FileText, LogOut } from 'lucide-react'
import { Profile } from '@/lib/types'
import { logout } from '@/lib/actions/auth'
import { motion, AnimatePresence } from 'framer-motion'

interface NewNavbarProps {
  user: Profile | null
  isDarkMode: boolean
  onToggleDarkMode: () => void
  onToggleSidebar: () => void
  isMobile: boolean
}

export function NewNavbar({ 
  user, 
  isDarkMode, 
  onToggleDarkMode, 
  onToggleSidebar, 
  isMobile 
}: NewNavbarProps) {
  const [isProfileOpen, setIsProfileOpen] = useState(false)

  return (
    <nav className={`h-20 transition-colors duration-300 relative z-10 ${
      isDarkMode
        ? 'bg-black'
        : 'bg-white/90 backdrop-blur-xl shadow-sm'
    }`}>
      <div className="h-full px-4 flex items-center">
        {/* Left Section */}
        <div className="flex items-center space-x-4 flex-1">
          {/* Mobile Hamburger */}
          {isMobile && (
            <button
              onClick={onToggleSidebar}
              className={`p-2 rounded-xl transition-all duration-300 ${
                isDarkMode
                  ? 'hover:bg-gray-900 text-gray-200'
                  : 'hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105 hover:shadow-lg'
              }`}
            >
              <Menu className="w-5 h-5" />
            </button>
          )}

          {/* Doctor Info - Similar to /info page */}
          <div className="hidden sm:block min-w-0">
            <div className={`text-lg font-bold ${
              isDarkMode ? 'text-gray-100' : 'text-amber-800'
            }`}>
              <h2 className="truncate">
                Dr. {user?.name || 'Doctor'}
              </h2>
            </div>
            <p className={`text-sm truncate ${
              isDarkMode ? 'text-gray-300' : 'text-amber-600'
            }`}>
              {user?.clinic_name || 'Medical Practice'}
            </p>
          </div>
        </div>

        {/* Center - Logo */}
        <div className="flex items-center space-x-3 flex-shrink-0">
          <div className="relative">
            <div className="relative w-12 h-12">
              <Image
                src="/icons/celer-ai-logo-navbar-50x50.png"
                alt="Celer AI - Medical Recording Interface"
                width={50}
                height={50}
                className="rounded-xl"
              />
            </div>
            {/* Keep the original orange blinking animation */}
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-400 rounded-full animate-ping"></div>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-400 rounded-full"></div>
          </div>
          <span className={`hidden sm:block text-xl font-bold ${
            isDarkMode ? 'text-gray-100' : 'text-slate-800'
          }`}>
            Celer AI
          </span>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2 flex-1 justify-end">
          {/* Dark Mode Toggle */}
          <button
            onClick={onToggleDarkMode}
            className={`p-2 rounded-xl transition-all duration-300 ${
              isDarkMode
                ? 'hover:bg-gray-900 text-gray-200'
                : 'hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105 hover:shadow-lg'
            }`}
          >
            {isDarkMode ? (
              <Sun className="w-5 h-5" />
            ) : (
              <Moon className="w-5 h-5" />
            )}
          </button>

          {/* Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsProfileOpen(!isProfileOpen)}
              className={`p-2 rounded-xl transition-all duration-300 ${
                isDarkMode
                  ? 'hover:bg-gray-900 text-gray-200'
                  : 'hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105 hover:shadow-lg'
              }`}
            >
              <User className="w-5 h-5" />
            </button>

            {/* Profile Dropdown Menu */}
            <AnimatePresence>
              {isProfileOpen && (
                <>
                  {/* Backdrop */}
                  <div 
                    className="fixed inset-0 z-10"
                    onClick={() => setIsProfileOpen(false)}
                  />
                  
                  {/* Dropdown */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                    transition={{ duration: 0.15 }}
                    className={`absolute right-0 mt-2 w-48 rounded-xl shadow-2xl z-50 ${
                      isDarkMode
                        ? 'bg-gray-950 border border-gray-800'
                        : 'bg-white/95 backdrop-blur-xl border border-white/30 shadow-indigo-100/50'
                    }`}
                  >
                    <div className="py-1">
                      {/* User Info */}
                      <div className={`px-4 py-2 border-b ${
                        isDarkMode ? 'border-gray-700' : 'border-gray-200'
                      }`}>
                        <p className={`text-sm font-medium ${
                          isDarkMode ? 'text-white' : 'text-gray-900'
                        }`}>
                          Dr. {user?.name || 'Doctor'}
                        </p>
                        <p className={`text-xs ${
                          isDarkMode ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {user?.clinic_name || 'Medical Practice'}
                        </p>
                      </div>

                      {/* Menu Items */}
                      <Link
                        href="/templates"
                        className={`flex items-center px-4 py-2 text-sm transition-colors ${
                          isDarkMode 
                            ? 'text-gray-300 hover:bg-gray-700' 
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <FileText className="w-4 h-4 mr-3" />
                        Templates
                      </Link>

                      <Link
                        href="/settings"
                        className={`flex items-center px-4 py-2 text-sm transition-colors ${
                          isDarkMode 
                            ? 'text-gray-300 hover:bg-gray-700' 
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <Settings className="w-4 h-4 mr-3" />
                        Settings
                      </Link>

                      <Link
                        href="/info"
                        className={`flex items-center px-4 py-2 text-sm transition-colors ${
                          isDarkMode 
                            ? 'text-gray-300 hover:bg-gray-700' 
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <Info className="w-4 h-4 mr-3" />
                        Info
                      </Link>

                      <div className={`border-t ${
                        isDarkMode ? 'border-gray-700' : 'border-gray-200'
                      }`}>
                        <form action={logout}>
                          <button
                            type="submit"
                            className={`flex items-center w-full px-4 py-2 text-sm transition-colors ${
                              isDarkMode 
                                ? 'text-red-400 hover:bg-gray-700' 
                                : 'text-red-600 hover:bg-gray-100'
                            }`}
                            onClick={() => setIsProfileOpen(false)}
                          >
                            <LogOut className="w-4 h-4 mr-3" />
                            Logout
                          </button>
                        </form>
                      </div>
                    </div>
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </nav>
  )
}
