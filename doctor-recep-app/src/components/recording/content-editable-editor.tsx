'use client'

import { useRef, useEffect } from 'react'
import { Bold, Italic } from 'lucide-react'

interface ContentEditableEditorProps {
  content: string
  onChange: (content: string) => void
  isEditing: boolean
  isDarkMode: boolean
}

// Convert markdown to HTML for display
const markdownToHtml = (markdown: string): string => {
  return markdown
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

// Convert HTML to markdown for backend
const htmlToMarkdown = (html: string): string => {
  return html
    .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
    .replace(/<b>(.*?)<\/b>/g, '**$1**')
    .replace(/<em>(.*?)<\/em>/g, '*$1*')
    .replace(/<i>(.*?)<\/i>/g, '*$1*')
    .replace(/<br\s*\/?>/g, '\n')
    .replace(/<div>/g, '\n')
    .replace(/<\/div>/g, '')
    .replace(/<p>/g, '')
    .replace(/<\/p>/g, '\n')
    .trim()
}



export function ContentEditableEditor({ content, onChange, isEditing, isDarkMode }: ContentEditableEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)

  // Convert markdown content to HTML for display
  const htmlContent = markdownToHtml(content)

  useEffect(() => {
    if (editorRef.current && !isEditing) {
      editorRef.current.innerHTML = htmlContent
    }
  }, [htmlContent, isEditing])

  const handleInput = () => {
    if (editorRef.current && isEditing) {
      const html = editorRef.current.innerHTML
      const markdown = htmlToMarkdown(html)
      onChange(markdown)
    }
  }

  const toggleBold = () => {
    document.execCommand('bold', false)
    handleInput()
  }

  const toggleItalic = () => {
    document.execCommand('italic', false)
    handleInput()
  }



  return (
    <div className={`min-h-[200px] ${
      isEditing
        ? ''
        : isDarkMode
          ? 'text-gray-200'
          : 'text-slate-800'
    }`}>
      {/* Toolbar - only show when editing */}
      {isEditing && (
        <div className="flex gap-2 mb-2 border-b pb-2">
          <button
            onClick={toggleBold}
            className={`p-2 rounded transition-colors ${
              isDarkMode
                ? 'hover:bg-gray-700 text-gray-300'
                : 'hover:bg-gray-100 text-gray-700'
            }`}
            title="Bold"
          >
            <Bold className="w-4 h-4" />
          </button>
          <button
            onClick={toggleItalic}
            className={`p-2 rounded transition-colors ${
              isDarkMode
                ? 'hover:bg-gray-700 text-gray-300'
                : 'hover:bg-gray-100 text-gray-700'
            }`}
            title="Italic"
          >
            <Italic className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable={isEditing}
        onInput={handleInput}
        dangerouslySetInnerHTML={{ __html: htmlContent }}
        className={`prose max-w-none focus:outline-none min-h-[100px] p-4 rounded-lg ${
          isDarkMode
            ? 'prose-invert text-gray-200'
            : 'text-slate-800'
        } ${
          isEditing
            ? isDarkMode
              ? 'bg-black border border-gray-700'
              : 'bg-white/70 backdrop-blur-sm border border-white/30'
            : isDarkMode
              ? 'bg-black border border-gray-700'
              : 'bg-white/70 backdrop-blur-sm border border-white/30'
        }`}
        style={{
          whiteSpace: 'pre-wrap',
          wordWrap: 'break-word'
        }}
        suppressContentEditableWarning={true}
      />
    </div>
  )
}
