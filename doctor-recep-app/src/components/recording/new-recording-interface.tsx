'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Profile, Consultation } from '@/lib/types'
import { ConsultationsSidebar } from './consultations-sidebar'
import { RecordingMainArea } from './recording-main-area'
import { motion, AnimatePresence } from 'framer-motion'
import { Menu, Moon, Sun, User, Settings, Info, LogOut } from 'lucide-react'
import { logout } from '@/lib/actions/auth'

interface NewRecordingInterfaceProps {
  user: Profile | null
  consultations: Consultation[]
  hasMore: boolean
  doctorId: string
}

export function NewRecordingInterface({ user, consultations, hasMore, doctorId }: NewRecordingInterfaceProps) {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const [isProfileOpen, setIsProfileOpen] = useState(false)

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 640)
      // Auto-close sidebar on mobile when screen size changes
      if (window.innerWidth > 640) {
        setIsSidebarOpen(false)
      }
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Check for dark mode preference - default to light mode
  useEffect(() => {
    const darkModePreference = localStorage.getItem('darkMode')
    if (darkModePreference) {
      setIsDarkMode(darkModePreference === 'true')
    } else {
      // Default to light mode (beige theme)
      setIsDarkMode(false)
    }
  }, [])

  // Apply dark mode to document
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
    localStorage.setItem('darkMode', isDarkMode.toString())
  }, [isDarkMode])

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
  }

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  const handleConsultationSelect = (consultation: Consultation) => {
    setSelectedConsultation(consultation)
    // Close sidebar on mobile after selection
    if (isMobile) {
      setIsSidebarOpen(false)
    }
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 overflow-x-hidden ${
      isDarkMode
        ? 'bg-black text-gray-100'
        : 'bg-gradient-to-br from-indigo-50 via-white to-cyan-50 text-gray-900'
    }`}>
      {/* Background Elements - Only in light mode */}
      {!isDarkMode && (
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/10 to-pink-200/10 rounded-full blur-xl animate-pulse delay-2000"></div>
        </div>
      )}
      {/* Floating Navigation - Hide when mobile sidebar is open */}
      <nav className={`fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20 transition-opacity duration-300 ${
        isMobile && isSidebarOpen ? 'opacity-0 pointer-events-none' : 'opacity-100'
      }`}>
        <div className={`flex items-center ${isMobile ? 'space-x-4' : 'space-x-8'}`}>
          <div className="flex items-center space-x-3">
            <div className="relative w-10 h-10">
              <Image
                src="/icons/celer-ai-logo-navbar-40x40.png"
                alt="Celer AI - Medical Documentation Platform"
                width={40}
                height={40}
                className="rounded-lg"
              />
            </div>
            {!isMobile && (
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
                Celer AI
              </span>
            )}
          </div>

          <div className={`flex items-center ${isMobile ? 'space-x-2' : 'space-x-3'}`}>
            {/* Mobile Hamburger */}
            {isMobile && (
              <button
                onClick={toggleSidebar}
                className="p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105"
              >
                <Menu className="w-4 h-4" />
              </button>
            )}

            {/* Dark Mode Toggle */}
            <button
              onClick={toggleDarkMode}
              className="p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105"
            >
              {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
            </button>

            {/* Navigation Links */}
            {!isMobile ? (
              <>
                <Link
                  href="/info"
                  className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
                >
                  Analytics
                </Link>
                <Link
                  href="/settings"
                  className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
                >
                  Settings
                </Link>
              </>
            ) : (
              <>
                <Link
                  href="/info"
                  className="p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105"
                  title="Analytics"
                >
                  <Info className="w-4 h-4" />
                </Link>
                <Link
                  href="/settings"
                  className="p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105"
                  title="Settings"
                >
                  <Settings className="w-4 h-4" />
                </Link>
              </>
            )}

            {/* Profile Dropdown */}
            <div className="relative">
              <button
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105"
              >
                <User className="w-4 h-4" />
              </button>

              <AnimatePresence>
                {isProfileOpen && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                    transition={{ duration: 0.15 }}
                    className="absolute right-0 mt-2 w-48 rounded-xl shadow-2xl z-50 bg-white/95 backdrop-blur-xl border border-white/30"
                  >
                    <div className="py-1">
                      <div className="px-4 py-2 border-b border-gray-200">
                        <p className="text-sm font-medium text-gray-900">
                          Dr. {user?.name || 'Doctor'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {user?.email}
                        </p>
                      </div>

                      <form action={logout}>
                        <button
                          type="submit"
                          className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 transition-colors"
                          onClick={() => setIsProfileOpen(false)}
                        >
                          <LogOut className="w-4 h-4 mr-3" />
                          Logout
                        </button>
                      </form>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </nav>

      <div className={`flex h-screen pt-20 ${
        isDarkMode ? 'bg-black' : 'bg-gradient-to-br from-indigo-50 via-white to-cyan-50'
      }`}>
        {/* Desktop Sidebar */}
        {!isMobile && (
          <div className="w-80 border-r-0">
            <ConsultationsSidebar
              consultations={consultations}
              hasMore={hasMore}
              onConsultationSelect={handleConsultationSelect}
              selectedConsultation={selectedConsultation}
              isDarkMode={isDarkMode}
              doctorId={doctorId}
            />
          </div>
        )}

        {/* Mobile Sidebar Overlay */}
        <AnimatePresence>
          {isMobile && isSidebarOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 z-40"
                onClick={() => setIsSidebarOpen(false)}
              />
              
              {/* Sidebar */}
              <motion.div
                initial={{ x: '-100%' }}
                animate={{ x: 0 }}
                exit={{ x: '-100%' }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                className={`fixed left-0 top-0 bottom-0 w-[75%] z-40 transition-colors duration-300 ${
                  isDarkMode
                    ? 'bg-gray-950'
                    : 'bg-gradient-to-br from-indigo-50 via-white to-cyan-50'
                }`}
              >
                <ConsultationsSidebar
                  consultations={consultations}
                  hasMore={hasMore}
                  onConsultationSelect={handleConsultationSelect}
                  selectedConsultation={selectedConsultation}
                  isDarkMode={isDarkMode}
                  doctorId={doctorId}
                  isMobile={true}
                  onClose={() => setIsSidebarOpen(false)}
                />
              </motion.div>
            </>
          )}
        </AnimatePresence>

        {/* Main Recording Area */}
        <div className="flex-1 overflow-hidden">
          <RecordingMainArea
            selectedConsultation={selectedConsultation}
            isDarkMode={isDarkMode}
            doctorId={doctorId}
            doctorName={user?.name || undefined}
            onConsultationUpdate={(updated) => {
              setSelectedConsultation(updated)
              // You might want to refresh consultations list here
            }}
          />
        </div>
      </div>
    </div>
  )
}
