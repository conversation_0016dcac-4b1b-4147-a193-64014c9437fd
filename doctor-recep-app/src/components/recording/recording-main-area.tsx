'use client'

import { useState, useRef, useEffect } from 'react'
import { Consultation } from '@/lib/types'
import { StreamlinedRecordingArea } from './streamlined-recording-area'
import { motion } from 'framer-motion'

interface RecordingMainAreaProps {
  selectedConsultation: Consultation | null
  isDarkMode: boolean
  doctorId: string
  doctorName?: string
  onConsultationUpdate: (consultation: Consultation) => void
}

export function RecordingMainArea({
  selectedConsultation,
  isDarkMode,
  doctorId,
  doctorName,
  onConsultationUpdate
}: RecordingMainAreaProps) {
  // Recording state
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [recordingDuration, setRecordingDuration] = useState(0)
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const [audioFile, setAudioFile] = useState<File | null>(null)
  
  // Form state
  const [patientName, setPatientName] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState('outpatient')
  const [additionalNotes, setAdditionalNotes] = useState('')
  
  // Media state
  const [images, setImages] = useState<Array<{ id: string; file: File; preview?: string }>>([])
  
  // Summary state
  const [isGenerating, setIsGenerating] = useState(false)
  const [summary, setSummary] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  
  // Refs
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Load consultation data when selected
  useEffect(() => {
    if (selectedConsultation) {
      setPatientName(selectedConsultation.patient_name || '')
      setSelectedTemplate(selectedConsultation.consultation_type || 'outpatient')
      setAdditionalNotes(selectedConsultation.additional_notes || '')
      setSummary(selectedConsultation.edited_note || selectedConsultation.ai_generated_note || '')
      setIsEditing(false)
      // Clear recording state when viewing existing consultation
      setAudioBlob(null)
      setAudioFile(null)
      setImages([])
      setIsRecording(false)
      setIsPaused(false)
      setRecordingDuration(0)
    } else {
      // Clear all state for new recording
      setPatientName('')
      setSelectedTemplate('outpatient')
      setAdditionalNotes('')
      setSummary('')
      setIsEditing(false)
      setAudioBlob(null)
      setAudioFile(null)
      setImages([])
      setIsRecording(false)
      setIsPaused(false)
      setRecordingDuration(0)
    }
  }, [selectedConsultation])

  // Recording timer
  useEffect(() => {
    if (isRecording && !isPaused) {
      intervalRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1)
      }, 1000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isRecording, isPaused])

  // Get the best supported audio format for this device
  const getSupportedMimeType = () => {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/mpeg'
    ]
    return types.find(type => MediaRecorder.isTypeSupported(type)) || 'audio/webm'
  }

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
        }
      })

      streamRef.current = stream

      const mimeType = getSupportedMimeType()
      const mediaRecorder = new MediaRecorder(stream, { mimeType })
      mediaRecorderRef.current = mediaRecorder

      const chunks: Blob[] = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: mimeType })

        // Generate appropriate file extension
        const getFileExtension = (mimeType: string) => {
          if (mimeType.includes('webm')) return 'webm'
          if (mimeType.includes('mp4')) return 'mp4'
          if (mimeType.includes('mpeg')) return 'mp3'
          return 'webm'
        }

        const extension = getFileExtension(mimeType)
        const file = new File([blob], `recording_${Date.now()}.${extension}`, {
          type: mimeType
        })

        setAudioBlob(blob)
        setAudioFile(file)
        setIsRecording(false)
        setIsPaused(false)

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop())
      }

      mediaRecorder.start()
      setIsRecording(true)
      setIsPaused(false)
      setRecordingDuration(0)

    } catch (error) {
      console.error('Recording error:', error)
      alert('Failed to start recording. Please check microphone permissions.')
    }
  }

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      if (isPaused) {
        mediaRecorderRef.current.resume()
        setIsPaused(false)
      } else {
        mediaRecorderRef.current.pause()
        setIsPaused(true)
      }
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
    }
  }

  // Handle image upload (matching /record page implementation)
  const handleImageUpload = async (files: FileList) => {
    try {
      const newImages: Array<{ id: string, file: File, preview?: string }> = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        if (!file.type.startsWith('image/')) continue
        if (file.size > 10 * 1024 * 1024) continue // 10MB limit

        const preview = URL.createObjectURL(file)

        newImages.push({
          id: `${Date.now()}-${i}`,
          file,
          preview
        })
      }

      setImages(prev => [...prev, ...newImages])
    } catch {
      console.error('Failed to process images')
    }
  }

  const removeImage = (id: string) => {
    setImages(prev => {
      const image = prev.find(img => img.id === id)
      if (image && image.preview) {
        URL.revokeObjectURL(image.preview)
      }
      return prev.filter(img => img.id !== id)
    })
  }

  return (
    <div className={`h-full flex flex-col relative ${
      isDarkMode ? 'bg-transparent' : 'bg-transparent'
    }`}>
      {/* Main Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-5xl mx-auto"
        >
          <StreamlinedRecordingArea
            selectedConsultation={selectedConsultation}
            isDarkMode={isDarkMode}
            doctorId={doctorId}
            doctorName={doctorName}
            onConsultationUpdate={(updated) => {
              onConsultationUpdate(updated)
              // SEAMLESS TRANSITION: Clear recording state when consultation is saved
              if (updated && !selectedConsultation) {
                // Clear all local state to show saved content
                setAudioBlob(null)
                setAudioFile(null)
                setImages([])
                setIsGenerating(false)
              }
            }}
            // Pass all the state and handlers
            patientName={patientName}
            setPatientName={setPatientName}
            selectedTemplate={selectedTemplate}
            setSelectedTemplate={setSelectedTemplate}
            isRecording={isRecording}
            isPaused={isPaused}
            recordingDuration={recordingDuration}
            audioBlob={audioBlob}
            audioFile={audioFile}
            images={images}
            setImages={setImages}
            isGenerating={isGenerating}
            setIsGenerating={setIsGenerating}
            summary={summary}
            setSummary={setSummary}
            isEditing={isEditing}
            setIsEditing={setIsEditing}
            additionalNotes={additionalNotes}
            setAdditionalNotes={setAdditionalNotes}
            startRecording={startRecording}
            pauseRecording={pauseRecording}
            stopRecording={stopRecording}
            handleImageUpload={handleImageUpload}
            removeImage={removeImage}
            clearAudio={() => {
              setAudioBlob(null)
              setAudioFile(null)
            }}
          />
        </motion.div>
      </div>
    </div>
  )
}
