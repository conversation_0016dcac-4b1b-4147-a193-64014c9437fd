'use client'

import { useState, useEffect } from 'react'
import { ThumbsUp, ThumbsDown, X, Star } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { createContactRequest } from '@/lib/actions/contact-requests'

interface DashboardFeedbackWidgetProps {
  user?: {
    id: string
    name?: string
    email?: string
  } | null
}

/**
 * Custom feedback widget following navbar isolation principles:
 * - Fixed positioning with high z-index
 * - Self-contained state management
 * - No interference with dashboard components
 * - Positioned in top-right corner aligned with navbar
 */
export function DashboardFeedbackWidget({ user }: DashboardFeedbackWidgetProps) {
  const [mounted, setMounted] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  
  // Form state
  const [category, setCategory] = useState<'feedback' | 'feature' | 'bug' | 'billing'>('feedback')
  const [priority, setPriority] = useState<1 | 2 | 3 | 4 | 5>(3)
  const [message, setMessage] = useState('')

  // Handle client-side mounting (navbar isolation pattern)
  useEffect(() => {
    setMounted(true)
  }, [])

  const openModal = (presetCategory?: 'feedback' | 'feature' | 'bug' | 'billing', presetPriority?: 1 | 2 | 3 | 4 | 5, presetMessage?: string) => {
    if (presetCategory) setCategory(presetCategory)
    if (presetPriority) setPriority(presetPriority)
    if (presetMessage) setMessage(presetMessage)
    setShowModal(true)
    setSubmitSuccess(false)
  }

  const closeModal = () => {
    setShowModal(false)
    setIsSubmitting(false)
    setSubmitSuccess(false)
    // Reset form
    setCategory('feedback')
    setPriority(3)
    setMessage('')
  }

  const handleQuickFeedback = (type: 'positive' | 'negative') => {
    if (type === 'positive') {
      openModal('feedback', 5, '👍 Love it! Great experience with Celer AI.')
    } else {
      openModal('feedback', 2, '👎 Needs improvement. Could be better.')
    }
  }

  const handleSubmit = async () => {
    if (!user?.id || !message.trim()) return

    setIsSubmitting(true)
    
    try {
      const result = await createContactRequest(
        user.id,
        message,
        priority.toString(), // Use priority as subject
        category // Use category as request_type
      )

      if (result.success) {
        setSubmitSuccess(true)
        // Auto-close after 3 seconds
        setTimeout(() => {
          closeModal()
        }, 3000)
      } else {
        console.error('Failed to submit feedback:', result.error)
      }
    } catch (error) {
      console.error('Error submitting feedback:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Don't render until mounted on client (navbar isolation pattern)
  if (!mounted) {
    return null
  }

  return (
    <>
      {/* Custom feedback widget positioned top-right corner */}
      <div className="celerai-feedback-widget fixed top-4 right-4 z-[60]">
        {/* Feedback form container - slides in from right */}
        <AnimatePresence>
          {showModal && (
            <motion.div 
              initial={{ opacity: 0, x: 20, scale: 0.95 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 20, scale: 0.95 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="absolute top-0 right-12 w-80 bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden"
            >
              {/* Header */}
              <div className="px-4 py-3 bg-gradient-to-r from-gray-50 to-slate-100 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center">
                      <Image
                        src="/icons/celer-ai-logo-navbar-40x40.png"
                        alt="Celer AI Feedback"
                        width={32}
                        height={32}
                        className="rounded-lg"
                      />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-slate-800">Quick Feedback</h3>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Form Body */}
              <div className="p-4 space-y-3">
                {submitSuccess ? (
                  // Success message
                  <motion.div 
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.4, ease: "easeOut" }}
                    className="text-center py-2"
                  >
                    <motion.div 
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, duration: 0.3, ease: "easeOut" }}
                      className="text-2xl mb-2"
                    >
                      ✅
                    </motion.div>
                    <p className="text-sm text-slate-600">Thank you for your feedback!</p>
                  </motion.div>
                ) : (
                  <>
                    {/* Category Selection */}
                    <div>
                      <div className="flex flex-wrap gap-1">
                        {(['feedback', 'feature', 'bug', 'billing'] as const).map((cat) => (
                          <button
                            key={cat}
                            onClick={() => setCategory(cat)}
                            className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                              category === cat
                                ? 'bg-teal-100 text-teal-700'
                                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                            }`}
                          >
                            {cat === 'feedback' ? 'Feedback' : 
                             cat === 'feature' ? 'Feature' : 
                             cat === 'bug' ? 'Bug' : 'Billing'}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Priority Selection */}
                    <div>
                      <div className="flex space-x-1">
                        {[1, 2, 3, 4, 5].map((level) => (
                          <button
                            key={level}
                            onClick={() => setPriority(level as 1 | 2 | 3 | 4 | 5)}
                            className={`w-6 h-6 rounded transition-colors flex items-center justify-center text-sm ${
                              priority >= level
                                ? category === 'feedback'
                                  ? 'text-yellow-400'
                                  : 'text-red-500'
                                : 'text-gray-300 hover:text-gray-400'
                            }`}
                          >
                            {category === 'feedback' ? (
                              <Star className="w-full h-full fill-current" />
                            ) : (
                              <span className="font-bold">!</span>
                            )}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Message */}
                    <div>
                      <textarea
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder={
                          category === 'feedback' 
                            ? 'What did you like or dislike?' 
                            : category === 'feature'
                            ? 'What feature would you like?'
                            : category === 'bug'
                            ? 'What bug did you encounter?'
                            : 'What billing issue can we help with?'
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-1 focus:ring-teal-500 focus:border-teal-500 text-sm text-slate-800 bg-white resize-none"
                        rows={3}
                      />
                    </div>

                    {/* Submit Button */}
                    <button
                      onClick={handleSubmit}
                      disabled={isSubmitting || (category !== 'feedback' && !message.trim())}
                      className="w-full bg-teal-600 hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      {isSubmitting ? 'Submitting...' : 'Submit'}
                    </button>
                  </>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main widget with hover buttons - larger hover area for better UX */}
        <div
          className="relative group p-4"
          onMouseEnter={() => setIsExpanded(true)}
          onMouseLeave={() => setIsExpanded(false)}
        >
          {/* Quick feedback buttons - slide down BELOW main button */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="absolute top-16 left-1/2 transform -translate-x-1/2 flex space-x-2"
              >
                {/* Thumbs Down (left) */}
                <button
                  onClick={() => handleQuickFeedback('negative')}
                  className="w-8 h-8 rounded-full bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center justify-center"
                  title="Needs improvement 👎"
                >
                  <ThumbsDown className="w-3.5 h-3.5" />
                </button>

                {/* Thumbs Up (right) */}
                <button
                  onClick={() => handleQuickFeedback('positive')}
                  className="w-8 h-8 rounded-full bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center justify-center"
                  title="Love it! 👍"
                >
                  <ThumbsUp className="w-3.5 h-3.5" />
                </button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Celer AI feedback button - clean logo only */}
          <button
            onClick={() => openModal()}
            className="w-10 h-10 rounded-lg hover:scale-105 transition-all duration-200 flex items-center justify-center"
            title="Send feedback"
          >
            <Image
              src="/icons/celer-ai-logo-navbar-40x40.png"
              alt="Celer AI Feedback"
              width={40}
              height={40}
              className="rounded-lg shadow-lg hover:shadow-xl"
            />
          </button>
        </div>
      </div>
    </>
  )
}
