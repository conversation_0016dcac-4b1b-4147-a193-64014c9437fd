'use client'

import { useState } from 'react'
import { X, Credit<PERSON>ard, CheckCircle } from 'lucide-react'

interface PaymentDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (paymentMethod: string, reference: string) => void
  transactionAmount: number
  doctorName: string
  isLoading?: boolean
}

export function PaymentDetailsModal({
  isOpen,
  onClose,
  onConfirm,
  transactionAmount,
  doctorName,
  isLoading = false
}: PaymentDetailsModalProps) {
  const [paymentMethod, setPaymentMethod] = useState('')
  const [reference, setReference] = useState('')

  if (!isOpen) return null

  const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`

  const handleConfirm = () => {
    onConfirm(paymentMethod, reference)
  }

  const handleClose = () => {
    setPaymentMethod('')
    setReference('')
    onClose()
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={handleClose}
        />

        {/* Modal */}
        <div className="inline-block w-full max-w-md my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          {/* Header */}
          <div className="px-6 py-4 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CreditCard className="w-6 h-6 text-green-600" />
                <div>
                  <h3 className="text-lg font-medium text-black">Mark Payment as Paid</h3>
                  <p className="text-sm text-black">Dr. {doctorName} - {formatCurrency(transactionAmount)}</p>
                </div>
              </div>
              <button
                onClick={handleClose}
                disabled={isLoading}
                className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-6 space-y-4">
            <div>
              <label htmlFor="paymentMethod" className="block text-sm font-medium text-black mb-2">
                Payment Method
              </label>
              <select
                id="paymentMethod"
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-black"
              >
                <option value="">Select payment method</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="upi">UPI</option>
                <option value="cash">Cash</option>
                <option value="cheque">Cheque</option>
                <option value="card">Card Payment</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div>
              <label htmlFor="reference" className="block text-sm font-medium text-black mb-2">
                Payment Reference (Optional)
              </label>
              <input
                type="text"
                id="reference"
                value={reference}
                onChange={(e) => setReference(e.target.value)}
                placeholder="Transaction ID, cheque number, etc."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-black placeholder-gray-500"
              />
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <div className="flex items-start space-x-2">
                <CheckCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-800">Confirmation Required</p>
                  <p className="text-yellow-700 mt-1">
                    Please confirm that you have received the payment of {formatCurrency(transactionAmount)} from Dr. {doctorName} before marking this transaction as paid.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 px-6 py-4 border-t border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              disabled={!paymentMethod || isLoading}
              className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Processing...' : 'Mark as Paid'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}