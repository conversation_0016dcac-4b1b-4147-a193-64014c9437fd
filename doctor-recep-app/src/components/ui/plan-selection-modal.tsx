'use client'

import { useState } from 'react'
import { X, CreditCard } from 'lucide-react'
import { type BillingPlan } from '@/lib/actions/billing'

interface PlanSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  onSelectPlan: (planId: string, planPrice: number) => void
  plans: BillingPlan[]
  doctorName: string
  isLoading?: boolean
}

export function PlanSelectionModal({
  isOpen,
  onClose,
  onSelectPlan,
  plans,
  doctorName,
  isLoading = false
}: PlanSelectionModalProps) {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)

  if (!isOpen) return null

  const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`

  const handleSubmit = () => {
    if (selectedPlan) {
      const plan = plans.find(p => p.id === selectedPlan)
      if (plan) {
        onSelectPlan(selectedPlan, plan.monthly_price)
      }
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <CreditCard className="w-5 h-5 text-teal-600" />
            <h3 className="text-lg font-semibold text-slate-800">Select Plan for Dr. {doctorName}</h3>
          </div>
          <button onClick={onClose} className="text-slate-400 hover:text-slate-600">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="space-y-3 mb-6">
          {plans.map((plan) => (
            <label
              key={plan.id}
              className={`block p-4 rounded-lg cursor-pointer transition-colors ${
                selectedPlan === plan.id
                  ? 'bg-teal-50 border-2 border-teal-500'
                  : 'bg-slate-50 border-2 border-transparent hover:bg-slate-100'
              }`}
            >
              <input
                type="radio"
                name="plan"
                value={plan.id}
                checked={selectedPlan === plan.id}
                onChange={(e) => setSelectedPlan(e.target.value)}
                className="sr-only"
              />
              <div className="flex justify-between items-center">
                <div>
                  <div className="font-medium text-slate-800">{plan.name}</div>
                  <div className="text-sm text-slate-600">{plan.quota_limit} consultations</div>
                </div>
                <div className="text-lg font-bold text-slate-800">
                  {formatCurrency(plan.monthly_price)}
                </div>
              </div>
            </label>
          ))}
        </div>

        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 px-4 py-2 text-slate-700 bg-slate-200 rounded-lg hover:bg-slate-300 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            disabled={!selectedPlan || isLoading}
            className="flex-1 px-4 py-2 text-white bg-teal-600 rounded-lg hover:bg-teal-700 disabled:opacity-50"
          >
            {isLoading ? 'Creating...' : 'Create Bill'}
          </button>
        </div>
      </div>
    </div>
  )
}