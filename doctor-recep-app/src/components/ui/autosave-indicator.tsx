'use client'

import { Check, Loader2, AlertCircle } from 'lucide-react'
import { AutoSaveStatus } from '@/hooks/useAutosave'

interface AutoSaveIndicatorProps {
  status: AutoSaveStatus
  onRetry?: () => void
  className?: string
  showText?: boolean
}

export function AutoSaveIndicator({ 
  status, 
  onRetry, 
  className = '',
  showText = true 
}: AutoSaveIndicatorProps) {
  if (status === 'idle') return null

  return (
    <div className={`flex items-center text-xs ${className}`}>
      {status === 'saving' && (
        <>
          <Loader2 className="w-3 h-3 mr-1 animate-spin text-blue-500" />
          {showText && <span className="text-slate-600">Saving...</span>}
        </>
      )}
      
      {status === 'saved' && (
        <>
          <Check className="w-3 h-3 mr-1 text-green-500" />
          {showText && <span className="text-green-600">Saved</span>}
        </>
      )}
      
      {status === 'error' && (
        <>
          <div
            className="w-3 h-3 mr-1 text-red-500 cursor-pointer hover:scale-110 transition-transform"
            onClick={onRetry}
            title="Click to retry save"
          >
            <AlertCircle className="w-full h-full" />
          </div>
          {showText && (
            <span className="text-red-600 cursor-pointer" onClick={onRetry}>
              Save failed - Click to retry
            </span>
          )}
        </>
      )}
    </div>
  )
}
