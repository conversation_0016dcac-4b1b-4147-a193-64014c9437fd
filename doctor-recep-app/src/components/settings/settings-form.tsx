'use client'

import { useState } from 'react'
import { Save, RotateCcw, Lock } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
interface SettingsFormProps {
  doctorId: string
  doctorName: string
  doctorEmail: string
}

export function SettingsForm({ doctorId, doctorName, doctorEmail }: SettingsFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState<'success' | 'error'>('success')
  const [isChangingPassword, setIsChangingPassword] = useState(false)

  const handleSave = async () => {
    setIsLoading(true)
    setMessage('')

    try {
      // Since we removed template config settings, just show success message
      setMessageType('success')
      setMessage('Settings updated successfully!')
    } catch {
      setMessageType('error')
      setMessage('An unexpected error occurred')
    } finally {
      setIsLoading(false)
      setTimeout(() => setMessage(''), 3000)
    }
  }

  const handleReset = () => {
    // No template config to reset anymore
    setMessage('')
  }

  const handlePasswordChange = async () => {
    if (isChangingPassword) return

    setIsChangingPassword(true)
    try {
      const supabase = createClient()
      const { error } = await supabase.auth.resetPasswordForEmail(doctorEmail, {
        redirectTo: `${window.location.origin}/auth/confirm?next=/reset-password`,
      })

      if (error) {
        setMessageType('error')
        setMessage('Failed to send password reset email. Please try again.')
      } else {
        setMessageType('success')
        setMessage('Password reset link sent to your email! ✨ Check your inbox.')
      }
    } catch (error) {
      setMessageType('error')
      setMessage('An unexpected error occurred')
    } finally {
      setIsChangingPassword(false)
      setTimeout(() => setMessage(''), 5000) // Longer timeout for email message
    }
  }

  return (
    <div className="space-y-8">
      {/* Doctor Info & Security */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
          <h3 className="text-lg font-medium text-slate-800 mb-2 sm:mb-0">Doctor Information</h3>
          <button
            onClick={handlePasswordChange}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 w-fit"
          >
            <Lock className="w-4 h-4" />
            <span>Change Password</span>
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-slate-600">Name:</span>
            <span className="ml-2 text-slate-800">{doctorName}</span>
          </div>
          <div>
            <span className="font-medium text-slate-600">Email:</span>
            <span className="ml-2 text-slate-800">{doctorEmail}</span>
          </div>
        </div>
      </div>



      {/* Status Message */}
      {message && (
        <div className={`p-4 rounded-xl border ${
          messageType === 'success'
            ? 'bg-gradient-to-r from-emerald-50 to-cyan-50 border-emerald-200'
            : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'
        }`}>
          <p className={`text-sm font-medium flex items-center space-x-2 ${
            messageType === 'success' ? 'text-emerald-800' : 'text-red-800'
          }`}>
            <span className={`w-2 h-2 rounded-full ${
              messageType === 'success' ? 'bg-emerald-400' : 'bg-red-400'
            }`}></span>
            <span>{message}</span>
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6 border-t border-white/30">
        <button
          onClick={handleReset}
          className="flex items-center space-x-2 px-4 py-3 border border-slate-200 text-sm font-medium rounded-xl text-slate-700 bg-white/70 hover:bg-gradient-to-r hover:from-slate-50 hover:to-indigo-50 transition-all duration-300 transform hover:scale-105 active:scale-95"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Reset to Defaults</span>
        </button>

        <button
          onClick={handleSave}
          disabled={isLoading}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          <Save className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>{isLoading ? 'Saving...' : 'Save Settings'}</span>
        </button>
      </div>



    </div>
  )
}
