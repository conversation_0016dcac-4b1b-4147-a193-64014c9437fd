'use client'

import { Share2 } from 'lucide-react'

interface ShareButtonProps {
  title: string
  excerpt?: string
  className?: string
}

export function ShareButton({ title, excerpt, className = '' }: ShareButtonProps) {
  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title,
          text: excerpt || '',
          url: window.location.href,
        })
      } else {
        await navigator.clipboard.writeText(window.location.href)
        // Use a toast notification instead of alert for better UX
        console.log('Link copied to clipboard!')
      }
    } catch (error) {
      console.error('Share failed:', error)
    }
  }

  return (
    <button
      onClick={handleShare}
      className={`group flex items-center space-x-2 bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 hover:from-indigo-700 hover:via-purple-700 hover:to-cyan-700 text-white px-8 py-4 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg ${className}`}
    >
      <Share2 className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
      <span>Share the Magic</span>
    </button>
  )
}