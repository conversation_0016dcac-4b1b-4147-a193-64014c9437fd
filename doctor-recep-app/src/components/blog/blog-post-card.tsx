'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Calendar, ArrowR<PERSON>, Sparkles } from 'lucide-react'
import { urlFor } from '@/lib/sanity/client'

interface BlogPostCardProps {
  post: {
    _id: string
    title: string
    slug: { current: string }
    excerpt?: string
    publishedAt: string
    author?: {
      name: string
      image?: any
    }
    mainImage?: any
    categories?: Array<{
      title: string
      slug: { current: string }
    }>
  }
}

export function BlogPostCard({ post }: BlogPostCardProps) {
  const publishedDate = new Date(post.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  // Analytics tracking with proper error handling
  const handleClick = () => {
    // Only track in browser environment with proper error handling
    if (typeof window !== 'undefined') {
      // Dynamic import to avoid SSR issues and reduce initial bundle
      import('@/lib/analytics').then(({ trackEvent }) => {
        trackEvent('blog_post_viewed', { page_title: post.title })
      }).catch(error => {
        console.warn('Analytics tracking failed:', error)
      })
    }
  }

  return (
    <article className="relative group">
      {/* Magical glow effect */}
      <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-0 group-hover:opacity-30 transition-all duration-500"></div>

      <div className="relative bg-white/90 backdrop-blur-xl rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden border border-white/20">
        {/* Featured Image */}
        {post.mainImage && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={urlFor(post.mainImage).width(400).height(300).fit('crop').auto('format').url()}
              alt={post.mainImage.alt || post.title}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-500"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />

            {/* Magical sparkle effect */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Sparkles className="w-5 h-5 text-white animate-pulse" />
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6">
          {/* Categories */}
          {post.categories && post.categories.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {post.categories.slice(0, 2).map((category) => (
                <span
                  key={category.slug.current}
                  className="px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-xs font-medium rounded-full border border-indigo-200"
                >
                  {category.title}
                </span>
              ))}
            </div>
          )}

          {/* Title */}
          <h2 className="text-xl font-bold text-slate-900 mb-3 line-clamp-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-indigo-600 group-hover:to-purple-600 transition-all duration-300">
            {post.title}
          </h2>

          {/* Excerpt */}
          {post.excerpt && (
            <p className="text-slate-600 mb-6 line-clamp-3 leading-relaxed">
              {post.excerpt}
            </p>
          )}

          {/* Meta */}
          <div className="flex items-center justify-between text-sm text-slate-500 mb-6">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>{publishedDate}</span>
              </div>
              {post.author && (
                <div className="flex items-center space-x-2">
                  {post.author.image && (
                    <Image
                      src={urlFor(post.author.image).width(24).height(24).fit('crop').auto('format').url()}
                      alt={post.author.name}
                      width={24}
                      height={24}
                      className="rounded-full border border-white/50"
                    />
                  )}
                  <span>{post.author.name}</span>
                </div>
              )}
            </div>
          </div>

          {/* Read More Link */}
          <Link
            href={`/blog/${post.slug.current}`}
            onClick={handleClick}
            className="group/link inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 hover:from-indigo-700 hover:via-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
          >
            <span>Read Insight</span>
            <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300" />
          </Link>
        </div>
      </div>
    </article>
  )
}