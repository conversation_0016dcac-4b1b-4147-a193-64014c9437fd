'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { ForgotPasswordSchema } from '@/lib/validations'
import { trackAuth } from '@/lib/analytics'
import { Mail, CheckCircle, AlertCircle } from 'lucide-react'

export function ForgotPasswordForm() {
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState('')
  const [fieldError, setFieldError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Reset states
    setError('')
    setFieldError('')
    setIsLoading(true)

    // Validate email
    const validation = ForgotPasswordSchema.safeParse({ email })
    if (!validation.success) {
      setFieldError(validation.error.errors[0]?.message || 'Invalid email')
      setIsLoading(false)
      return
    }

    try {
      // Track attempt
      trackAuth('password_reset_requested')

      const supabase = createClient()
      const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/confirm?next=/reset-password`,
      })

      if (resetError) {
        console.error('Password reset error:', resetError)
        setError('Failed to send reset email. Please try again.')
        trackAuth('password_reset_failed')
      } else {
        setSuccess(true)
        trackAuth('password_reset_email_sent')
      }
    } catch (err) {
      console.error('Unexpected error:', err)
      setError('An unexpected error occurred. Please try again.')
      trackAuth('password_reset_failed')
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="text-center space-y-6">
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>
        
        <div>
          <h3 className="text-2xl font-bold text-slate-900 mb-2">
            Magic Link Sent! ✨
          </h3>
          <p className="text-slate-600 mb-4">
            We&apos;ve sent a password reset link to <strong>{email}</strong>
          </p>
          <p className="text-sm text-slate-500">
            Check your email and click the link to reset your password. 
            The link will expire in 1 hour.
          </p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <Mail className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
            <div className="text-left">
              <p className="text-sm font-medium text-blue-900">
                Didn&apos;t receive the email?
              </p>
              <p className="text-sm text-blue-700 mt-1">
                Check your spam folder or{' '}
                <button
                  onClick={() => {
                    setSuccess(false)
                    setEmail('')
                  }}
                  className="font-medium underline hover:no-underline"
                >
                  try again
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">
            Email Address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900"
            placeholder="<EMAIL>"
          />
          {fieldError && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span className="w-1 h-1 bg-red-600 rounded-full"></span>
              <span>{fieldError}</span>
            </p>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-xl flex items-center space-x-3">
          <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
          <span className="text-red-800 text-sm">{error}</span>
        </div>
      )}

      <div>
        <button
          type="submit"
          disabled={isLoading}
          className="group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              <span>Sending Magic Link...</span>
            </>
          ) : (
            <>
              <Mail className="w-4 h-4" />
              <span>Send Reset Link</span>
            </>
          )}
        </button>
      </div>
    </form>
  )
}
