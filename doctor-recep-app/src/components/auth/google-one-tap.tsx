'use client'

import { useEffect, useRef } from 'react'
import Script from 'next/script'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { trackAuth } from '@/lib/analytics'

// Google One Tap types
interface CredentialResponse {
  credential: string
  select_by: string
}

interface GoogleAccounts {
  id: {
    initialize: (config: GoogleOneTapConfig) => void
    prompt: (callback?: (notification: any) => void) => void
    cancel: () => void
  }
}

interface GoogleOneTapConfig {
  client_id: string
  callback: (response: CredentialResponse) => void
  nonce?: string
  auto_select?: boolean
  cancel_on_tap_outside?: boolean
  use_fedcm_for_prompt?: boolean
  context?: 'signin' | 'signup' | 'use'
  ux_mode?: 'popup' | 'redirect'
  itp_support?: boolean
}

declare global {
  interface Window {
    google?: {
      accounts: GoogleAccounts
    }
  }
}

interface GoogleOneTapProps {
  context?: 'signin' | 'signup' | 'use'
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function GoogleOneTap({ 
  context = 'signin', 
  onSuccess, 
  onError 
}: GoogleOneTapProps) {
  const router = useRouter()
  const supabase = createClient()
  const initializationRef = useRef(false)
  const scriptLoadedRef = useRef(false)

  // Generate nonce for security
  const generateNonce = async (): Promise<[string, string]> => {
    const nonce = btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(32))))
    const encoder = new TextEncoder()
    const encodedNonce = encoder.encode(nonce)
    const hashBuffer = await crypto.subtle.digest('SHA-256', encodedNonce)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    const hashedNonce = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')
    return [nonce, hashedNonce]
  }

  // Create profile for Google users
  const createGoogleUserProfile = async (user: any, userData: any) => {
    try {
      // Extract name from Google user data
      const fullName = userData.name || user.user_metadata?.full_name || user.user_metadata?.name || ''
      const avatarUrl = userData.picture || user.user_metadata?.avatar_url || user.user_metadata?.picture || null

      // Check if profile already exists
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .single()

      if (existingProfile) {
        // Profile exists, just update avatar if needed
        if (avatarUrl) {
          await supabase
            .from('profiles')
            .update({ avatar_url: avatarUrl })
            .eq('id', user.id)
        }
        return
      }

      // Create new profile for Google user
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          email: user.email!,
          name: fullName,
          avatar_url: avatarUrl,
          role: 'doctor', // Default role
          approved: false, // Still needs approval
          clinic_name: null, // To be filled later
          phone: null, // To be filled later
        })

      if (profileError) {
        console.error('Error creating Google user profile:', profileError)
        throw new Error('Failed to create user profile')
      }

      console.log('Google user profile created successfully')
    } catch (error) {
      console.error('Error in createGoogleUserProfile:', error)
      throw error
    }
  }

  // Handle Google sign-in
  const handleGoogleSignIn = async (response: CredentialResponse) => {
    try {
      console.log('Google One Tap response received')
      
      // Track the attempt
      trackAuth(context === 'signup' ? 'google_signup_attempted' : 'google_login_attempted')

      const [nonce] = await generateNonce()

      // Sign in with Google ID token
      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: 'google',
        token: response.credential,
        nonce,
      })

      if (error) {
        console.error('Google sign-in error:', error)
        onError?.(error.message || 'Google sign-in failed')
        return
      }

      if (!data.user) {
        console.error('No user data received from Google sign-in')
        onError?.('No user data received')
        return
      }

      console.log('Google sign-in successful:', data.user.email)

      // Create/update profile for Google user
      await createGoogleUserProfile(data.user, data.user.user_metadata)

      // Check if user is approved
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('approved, role')
        .eq('id', data.user.id)
        .single()

      if (profileError) {
        console.error('Error fetching user profile:', profileError)
        onError?.('Failed to fetch user profile')
        return
      }

      // Check admin role restriction
      if (profile.role === 'admin' || profile.role === 'super_admin') {
        await supabase.auth.signOut()
        onError?.('Admin users must use the admin login portal.')
        return
      }

      // Track successful authentication
      trackAuth(context === 'signup' ? 'google_signup_successful' : 'google_login_successful')

      // Handle approval status
      if (!profile.approved) {
        // User needs approval - redirect to a completion page or show message
        onError?.('Your account is pending approval. Please complete your profile or contact support.')
        return
      }

      // Success callback
      onSuccess?.()

      // Redirect to dashboard
      router.push('/dashboard')

    } catch (error) {
      console.error('Error in handleGoogleSignIn:', error)
      onError?.(error instanceof Error ? error.message : 'An unexpected error occurred')
    }
  }

  // Initialize Google One Tap
  const initializeGoogleOneTap = async () => {
    if (initializationRef.current || !scriptLoadedRef.current) return
    
    try {
      console.log('Initializing Google One Tap')
      initializationRef.current = true

      // Check if user is already signed in
      const { data: { session } } = await supabase.auth.getSession()
      if (session) {
        console.log('User already signed in, skipping One Tap')
        return
      }

      // Check if Google API is available
      if (!window.google?.accounts?.id) {
        console.error('Google Identity Services not loaded')
        return
      }

      const [, hashedNonce] = await generateNonce()

      // Initialize Google One Tap
      window.google.accounts.id.initialize({
        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
        callback: handleGoogleSignIn,
        nonce: hashedNonce,
        auto_select: false, // Don't auto-select to give user control
        cancel_on_tap_outside: true,
        use_fedcm_for_prompt: true, // Modern FedCM support
        context: context,
        ux_mode: 'popup',
        itp_support: true, // Safari support
      })

      // Show the One Tap prompt
      window.google.accounts.id.prompt((notification) => {
        console.log('One Tap notification:', notification)
        if (notification.isNotDisplayed()) {
          console.log('One Tap not displayed:', notification.getNotDisplayedReason())
        }
        if (notification.isSkippedMoment()) {
          console.log('One Tap skipped:', notification.getSkippedReason())
        }
      })

    } catch (error) {
      console.error('Error initializing Google One Tap:', error)
    }
  }

  // Handle script load
  const handleScriptLoad = () => {
    console.log('Google Identity Services script loaded')
    scriptLoadedRef.current = true
    initializeGoogleOneTap()
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (window.google?.accounts?.id) {
        window.google.accounts.id.cancel()
      }
    }
  }, [])

  return (
    <>
      <Script
        src="https://accounts.google.com/gsi/client"
        onLoad={handleScriptLoad}
        strategy="afterInteractive"
      />
      {/* One Tap will render automatically in the top-right corner */}
    </>
  )
}
