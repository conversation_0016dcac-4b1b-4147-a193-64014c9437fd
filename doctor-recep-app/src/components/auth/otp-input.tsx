'use client'

import { useRef, useEffect, useState } from 'react'

interface OTPInputProps {
  length: number
  value: string
  onChange: (value: string) => void
  onComplete?: (value: string) => void
  disabled?: boolean
  error?: boolean
}

export function OTPInput({ 
  length = 4, 
  value, 
  onChange, 
  onComplete, 
  disabled = false,
  error = false 
}: OTPInputProps) {
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])
  const [focusedIndex, setFocusedIndex] = useState(0)

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length)
  }, [length])

  // Auto-focus first input on mount
  useEffect(() => {
    if (inputRefs.current[0] && !disabled) {
      inputRefs.current[0].focus()
    }
  }, [disabled])

  // Handle input change
  const handleChange = (index: number, inputValue: string) => {
    if (disabled) return

    // Only allow digits
    const digit = inputValue.replace(/\D/g, '').slice(-1)
    
    const newValue = value.split('')
    newValue[index] = digit
    
    // Pad with empty strings if needed
    while (newValue.length < length) {
      newValue.push('')
    }
    
    const updatedValue = newValue.join('')
    onChange(updatedValue)

    // Move to next input if digit was entered
    if (digit && index < length - 1) {
      inputRefs.current[index + 1]?.focus()
      setFocusedIndex(index + 1)
    }

    // Call onComplete if all digits are filled
    if (updatedValue.length === length && updatedValue.replace(/\D/g, '').length === length) {
      onComplete?.(updatedValue)
    }
  }

  // Handle key down events
  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return

    if (e.key === 'Backspace') {
      e.preventDefault()
      
      const newValue = value.split('')
      
      if (newValue[index]) {
        // Clear current input
        newValue[index] = ''
      } else if (index > 0) {
        // Move to previous input and clear it
        newValue[index - 1] = ''
        inputRefs.current[index - 1]?.focus()
        setFocusedIndex(index - 1)
      }
      
      onChange(newValue.join(''))
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus()
      setFocusedIndex(index - 1)
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus()
      setFocusedIndex(index + 1)
    }
  }

  // Handle paste
  const handlePaste = (e: React.ClipboardEvent) => {
    if (disabled) return
    
    e.preventDefault()
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, length)
    
    if (pastedData) {
      onChange(pastedData.padEnd(length, ''))
      
      // Focus the next empty input or the last input
      const nextIndex = Math.min(pastedData.length, length - 1)
      inputRefs.current[nextIndex]?.focus()
      setFocusedIndex(nextIndex)
      
      // Call onComplete if all digits are filled
      if (pastedData.length === length) {
        onComplete?.(pastedData)
      }
    }
  }

  const handleFocus = (index: number) => {
    setFocusedIndex(index)
  }

  return (
    <div className="flex justify-center space-x-3 pointer-events-auto relative z-[100]">
      {Array.from({ length }, (_, index) => (
        <input
          key={index}
          ref={(el) => {
            inputRefs.current[index] = el
          }}
          type="text"
          inputMode="numeric"
          pattern="\d*"
          maxLength={1}
          autoComplete={index === 0 ? "one-time-code" : "off"}
          value={value[index] || ''}
          onChange={(e) => handleChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onPaste={handlePaste}
          onFocus={() => handleFocus(index)}
          disabled={disabled}
          className={`
            w-12 h-12 text-center text-xl font-semibold border-2 rounded-xl
            transition-all duration-200 bg-white
            focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent
            pointer-events-auto relative z-[110]
            ${error
              ? 'border-red-300 text-red-900'
              : focusedIndex === index
                ? 'border-indigo-300'
                : 'border-slate-200'
            }
            ${disabled
              ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
              : 'hover:border-slate-300 cursor-text'
            }
          `}
          aria-label={`Digit ${index + 1}`}
        />
      ))}
    </div>
  )
}
