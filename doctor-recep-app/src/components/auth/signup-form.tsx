'use client'

import { useActionState, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { signup } from '@/lib/actions/auth'
import { FormState } from '@/lib/types'
import { trackAuth } from '@/lib/analytics'
import { PhoneVerification } from './phone-verification'

// CORRECTED initialState to conform to FormState interface
const initialState: FormState = {
  success: false,
  message: '',
  // fieldErrors is optional, so it can be omitted or set to an empty object if needed
  // fieldErrors: {},
}

interface SignupFormProps {
  referralCode?: string
}

export function SignupForm({ referralCode }: SignupFormProps) {
  const [state, formAction, isPending] = useActionState(signup, initialState)
  const router = useRouter()
  const [showPhoneVerification, setShowPhoneVerification] = useState(false)
  const [verificationData, setVerificationData] = useState<{ userId: string; phone: string } | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    clinic_name: '',
    phone: ''
  })

  // Handle phone verification requirement
  useEffect(() => {
    if (state?.success && state?.message === 'PHONE_VERIFICATION_REQUIRED') {
      setShowPhoneVerification(true)
      setVerificationData({
        userId: state.data?.userId || '',
        phone: state.data?.phone || ''
      })
      // Track signup completion (before verification)
      trackAuth('signup_completed')
    }
  }, [state?.success, state?.message, state?.data?.phone, state?.data?.userId])

  // Auto-redirect to login page after 3 seconds for successful account creation (legacy)
  useEffect(() => {
    if (state?.success && state?.message?.includes('Account created successfully')) {
      // Track successful signup completion
      trackAuth('signup_completed')

      const timer = setTimeout(() => {
        router.push('/login')
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [state?.success, state?.message, router])

  // Handle successful phone verification
  const handleVerificationSuccess = () => {
    // Phone verification is part of signup completion
    trackAuth('signup_completed')
    router.push('/login')
  }

  // Show phone verification component if needed
  // Don't return early - render phone verification within the same container structure

  // Handle form submission to track signup started
  const handleSubmit = (formDataSubmitted: FormData) => {
    // Save form data before submission to preserve on errors
    setFormData({
      name: formDataSubmitted.get('name') as string || '',
      email: formDataSubmitted.get('email') as string || '',
      password: formDataSubmitted.get('password') as string || '',
      clinic_name: formDataSubmitted.get('clinic_name') as string || '',
      phone: formDataSubmitted.get('phone') as string || ''
    })

    // Track signup attempt
    trackAuth('signup_started')

    // Call the original form action
    formAction(formDataSubmitted)
  }

  return (
    <>
      {showPhoneVerification && verificationData ? (
        <PhoneVerification
          phone={verificationData.phone}
          userId={verificationData.userId}
          onSuccess={handleVerificationSuccess}
          onBack={() => setShowPhoneVerification(false)}
        />
      ) : (
        <form action={handleSubmit} className="mt-8 space-y-6">
      {/* Hidden field for referral code */}
      {referralCode && (
        <input
          type="hidden"
          name="referral_code"
          value={referralCode}
        />
      )}
      
      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-slate-700 mb-2">
            Full Name
          </label>
          <input
            id="name"
            name="name"
            type="text"
            autoComplete="name"
            required
            defaultValue={formData.name}
            className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900"
            placeholder="Dr. John Doe"
          />
          {state?.fieldErrors?.name && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span className="w-1 h-1 bg-red-600 rounded-full"></span>
              <span>{state.fieldErrors.name[0]}</span>
            </p>
          )}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">
            Email Address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            defaultValue={formData.email}
            className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900"
            placeholder="<EMAIL>"
          />
          {state?.fieldErrors?.email && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span className="w-1 h-1 bg-red-600 rounded-full"></span>
              <span>{state.fieldErrors.email[0]}</span>
            </p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-slate-700 mb-2">
            Password
          </label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="new-password"
            required
            defaultValue={formData.password}
            className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900"
            placeholder="Min 8 chars, 1 letter, 1 number, 1 special char"
          />
          {state?.fieldErrors?.password && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span className="w-1 h-1 bg-red-600 rounded-full"></span>
              <span>{state.fieldErrors.password[0]}</span>
            </p>
          )}
        </div>

        <div>
          <label htmlFor="clinic_name" className="block text-sm font-medium text-slate-700 mb-2">
            Hospital Name
          </label>
          <input
            id="clinic_name"
            name="clinic_name"
            type="text"
            defaultValue={formData.clinic_name}
            className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900"
            placeholder="ABC Medical Center"
          />
          {state?.fieldErrors?.clinic_name && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span className="w-1 h-1 bg-red-600 rounded-full"></span>
              <span>{state.fieldErrors.clinic_name[0]}</span>
            </p>
          )}
        </div>

        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-slate-700 mb-2">
            Phone Number
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-4 z-10">
              <span className="text-2xl">🇮🇳</span>
            </div>
            <input
              id="phone"
              name="phone"
              type="tel"
              autoComplete="tel"
              defaultValue={formData.phone}
              className="w-full pl-14 pr-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white backdrop-blur-sm placeholder-slate-400 text-slate-900"
              placeholder="9876543210"
            />
          </div>
          {state?.fieldErrors?.phone && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span className="w-1 h-1 bg-red-600 rounded-full"></span>
              <span>{state.fieldErrors.phone[0]}</span>
            </p>
          )}
        </div>

        <div>
          <label htmlFor="referral_code" className="block text-sm font-medium text-slate-700 mb-2">
            Referral ID (Optional)
          </label>
          <input
            id="referral_id"
            name="referral_code"
            type="text"
            className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900"
            placeholder="Enter referral ID if you have one (optional)"
            defaultValue={referralCode || ''}
          />
          {state?.fieldErrors?.referral_code && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span className="w-1 h-1 bg-red-600 rounded-full"></span>
              <span>{state.fieldErrors.referral_id[0]}</span>
            </p>
          )}
        </div>

        {/* Terms of Service and Privacy Policy Checkbox */}
        <div className="flex items-start space-x-3">
          <input
            id="terms"
            name="terms"
            type="checkbox"
            required
            className="mt-1 w-4 h-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500 focus:ring-2"
          />
          <label htmlFor="terms" className="text-sm text-slate-600 leading-relaxed">
            I accept the{' '}
            <a href="/terms" target="_blank" className="text-indigo-600 hover:text-purple-600 font-medium transition-colors">
              Terms of Service
            </a>
            {' '}and{' '}
            <a href="/privacy" target="_blank" className="text-indigo-600 hover:text-purple-600 font-medium transition-colors">
              Privacy Policy
            </a>
          </label>
        </div>
      </div>

      {state?.message && (
        <div className={`rounded-xl p-4 border ${
          state.success
            ? 'bg-gradient-to-r from-emerald-50 to-cyan-50 border-emerald-200'
            : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'
        }`}>
          <p className={`text-sm font-medium flex items-center space-x-2 ${
            state.success ? 'text-emerald-800' : 'text-red-800'
          }`}>
            <span className={`w-2 h-2 rounded-full ${
              state.success ? 'bg-emerald-400' : 'bg-red-400'
            }`}></span>
            <span>{state.message}</span>
          </p>
        </div>
      )}

      <div>
        <button
          type="submit"
          disabled={isPending}
          className="group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2"
        >
          {isPending ? (
            <>
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              <span>Creating your magical account...</span>
            </>
          ) : (
            <>
              <span>Start Creating Magic</span>
              <svg className="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </>
          )}
        </button>
      </div>
    </form>
      )}
    </>
  )
}