'use client'

import { useRef } from 'react'
import { Camera, Upload, X } from 'lucide-react'
import { ImageCaptureState, ImageFile } from '@/lib/types'
// import { fileToBase64, supportsCamera } from '@/lib/utils' // <--- REMOVE fileToBase64
import { supportsCamera } from '@/lib/utils' // <--- <PERSON><PERSON><PERSON> supportsCamera
import Image from 'next/image'

interface ImageCaptureProps {
  imageState: ImageCaptureState
  onStateChange: (newState: Partial<ImageCaptureState>) => void
  isMobile?: boolean // Keep if used, otherwise remove
}

export function ImageCapture({ imageState, onStateChange, isMobile: _isMobile }: ImageCaptureProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const cameraInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (files: FileList) => {
    try {
      onStateChange({ error: undefined })

      const newImages: ImageFile[] = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        // Validate file type
        if (!file.type.startsWith('image/')) {
          onStateChange({ error: `File ${file.name} is not a valid image` })
          return
        }

        // Validate file size (max 10MB per file)
        if (file.size > 10 * 1024 * 1024) {
          onStateChange({ error: `File ${file.name} is too large. Please select files under 10MB.` })
          return
        }

        // OPTIMISTIC: No local preview state - store files for upload only
        // Final state will be shown after upload completes
        newImages.push({
          id: `${Date.now()}-${i}`,
          file: file, // Store the raw File object for upload
          name: file.name,
          type: file.type,
          size: file.size
          // No preview property - no local state display
        })
      }

      onStateChange({
        images: [...imageState.images, ...newImages]
      })
    } catch (error) {
      console.error('Error processing images:', error)
      onStateChange({ error: 'Failed to process images. Please try again.' })
    }
  }


  const handleCameraCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files && files.length > 0) {
      handleFileSelect(files)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files && files.length > 0) {
      handleFileSelect(files)
    }
  }

  const removeImage = (imageId: string) => {
    // OPTIMISTIC: No preview URLs to revoke since we don't create them
    onStateChange({
      images: imageState.images.filter(img => img.id !== imageId),
      error: undefined,
    })
  }

  const clearAllImages = () => {
    // OPTIMISTIC: No preview URLs to revoke since we don't create them
    onStateChange({
      images: [],
      error: undefined,
    })

    // Reset file inputs
    if (fileInputRef.current) fileInputRef.current.value = ''
    if (cameraInputRef.current) cameraInputRef.current.value = ''
  }

  const openCamera = () => {
    cameraInputRef.current?.click()
  }

  const openFileSelector = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-4">
      {/* Upload Controls */}
      <div className="grid grid-cols-1 gap-3">
        {/* Camera Capture */}
        {supportsCamera() && (
          <button
            onClick={openCamera}
            className="flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors"
          >
            <Camera className="w-6 h-6 text-gray-400" />
            <span className="text-sm font-medium text-gray-700">
              Take Photo
            </span>
          </button>
        )}

        {/* File Upload */}
        <button
          onClick={openFileSelector}
          className="flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors"
        >
          <Upload className="w-6 h-6 text-gray-400" />
          <span className="text-sm font-medium text-gray-700">
            Upload from Gallery
          </span>
        </button>
      </div>

      {/* Hidden file inputs */}
      <input
        ref={cameraInputRef}
        type="file"
        accept="image/*"
        capture="environment"
        onChange={handleCameraCapture}
        className="hidden"
      />
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* OPTIMISTIC: No local preview - show only file count for feedback */}
      {imageState.images.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">
              Selected Images ({imageState.images.length})
            </h4>
            <button
              onClick={clearAllImages}
              className="text-xs text-red-600 hover:text-red-800 underline"
            >
              Clear All
            </button>
          </div>

          <div className="space-y-2">
            {imageState.images.map((image) => (
              <div key={image.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Camera className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 truncate max-w-[200px]">
                      {image.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {(image.size / 1024 / 1024).toFixed(1)} MB
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeImage(image.id)}
                  className="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Display */}
      {imageState.error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-sm text-red-700">{imageState.error}</p>
        </div>
      )}
    </div>
  )
}
