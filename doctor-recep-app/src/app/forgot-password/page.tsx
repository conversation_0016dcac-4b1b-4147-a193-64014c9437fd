import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { ForgotPasswordForm } from '@/components/auth/forgot-password-form'
import { ArrowLeft, Shield, Zap, Key } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Forgot Password - Celer AI System',
  description: 'Reset your Celer AI account password',
}

export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-10 h-10">
              <Image
                src="/icons/celer-ai-logo-navbar-40x40.png"
                alt="Celer AI - Forgot Password"
                width={40}
                height={40}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/login"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Sign In
            </Link>
            <Link
              href="/"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Home
            </Link>
          </div>
        </div>
      </nav>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Main Content */}
      <div className="relative flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
                <Key className="w-8 h-8 text-white" />
              </div>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4 mt-16">
              Forgot Your
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse relative z-10">
                Password?
              </span>
            </h2>

            <p className="text-lg text-slate-600 mb-6">
              No worries! We&apos;ll send you a magic link to reset it
            </p>

            <div className="flex justify-center items-center space-x-6 text-slate-500 text-sm">
              <div className="flex items-center space-x-1">
                <Shield className="w-4 h-4 text-indigo-600" />
                <span>Secure Reset</span>
              </div>
              <div className="flex items-center space-x-1">
                <Zap className="w-4 h-4 text-emerald-500" />
                <span>Instant Delivery</span>
              </div>
            </div>
          </div>

          {/* Forgot Password Form Card */}
          <div className="relative">
            <div className="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse"></div>
            <div className="relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl p-8 border border-white/20">
              <ForgotPasswordForm />
            </div>
          </div>

          {/* Back to login link */}
          <div className="text-center">
            <div className="bg-white/60 backdrop-blur-xl rounded-xl p-4 border border-white/30">
              <p className="text-sm text-slate-600">
                Remember your password?{' '}
                <Link
                  href="/login"
                  className="font-medium text-indigo-600 hover:text-purple-600 transition-colors duration-200 inline-flex items-center space-x-1"
                >
                  <ArrowLeft className="w-3 h-3" />
                  <span>Back to sign in</span>
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
