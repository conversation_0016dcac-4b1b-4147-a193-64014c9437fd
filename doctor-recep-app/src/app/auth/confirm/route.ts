import { type EmailOtpType } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/'
  
  const redirectTo = request.nextUrl.clone()
  redirectTo.pathname = next
  
  if (token_hash && type) {
    const supabase = await createClient()
    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })
    if (!error) {
      // Token verified successfully, redirect to the next page
      return NextResponse.redirect(redirectTo)
    }
  }
  
  // Token verification failed or missing parameters
  // Redirect to our branded error page
  redirectTo.pathname = '/auth/error'
  redirectTo.searchParams.set('message', 'invalid_token')
  return NextResponse.redirect(redirectTo)
}
