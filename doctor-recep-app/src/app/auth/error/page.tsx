import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { AlertCircle, ArrowLeft, Mail } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Authentication Error - Celer AI System',
  description: 'Authentication error occurred',
}

interface AuthErrorPageProps {
  searchParams: { message?: string }
}

export default function AuthErrorPage({ searchParams }: AuthErrorPageProps) {
  const errorMessage = searchParams.message

  const getErrorContent = () => {
    switch (errorMessage) {
      case 'invalid_token':
        return {
          title: 'Invalid or Expired Link',
          description: 'The password reset link you clicked is invalid or has expired.',
          suggestion: 'Please request a new password reset link.',
        }
      default:
        return {
          title: 'Authentication Error',
          description: 'An error occurred during authentication.',
          suggestion: 'Please try again or contact support if the problem persists.',
        }
    }
  }

  const errorContent = getErrorContent()

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-10 h-10">
              <Image
                src="/icons/celer-ai-logo-navbar-40x40.png"
                alt="Celer AI - Authentication Error"
                width={40}
                height={40}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/login"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Sign In
            </Link>
            <Link
              href="/"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Home
            </Link>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle className="w-8 h-8 text-red-600" />
              </div>
            </div>
            
            <h2 className="text-3xl md:text-4xl font-black text-slate-900 leading-none mb-4 mt-16">
              {errorContent.title}
            </h2>

            <p className="text-lg text-slate-600 mb-6">
              {errorContent.description}
            </p>

            <p className="text-sm text-slate-500">
              {errorContent.suggestion}
            </p>
          </div>

          {/* Action Card */}
          <div className="relative">
            <div className="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse"></div>
            <div className="relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl p-8 border border-white/20">
              <div className="space-y-4">
                {errorMessage === 'invalid_token' ? (
                  <Link
                    href="/forgot-password"
                    className="group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
                  >
                    <Mail className="w-4 h-4" />
                    <span>Request New Reset Link</span>
                  </Link>
                ) : (
                  <Link
                    href="/login"
                    className="group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    <span>Back to Login</span>
                  </Link>
                )}
                
                <Link
                  href="/"
                  className="w-full text-center text-slate-600 hover:text-slate-900 py-2 text-sm font-medium transition-colors"
                >
                  Go to Homepage
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
