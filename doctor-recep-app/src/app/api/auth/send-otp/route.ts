import { NextRequest, NextResponse } from 'next/server'
import { createVerifyNowService, formatPhoneNumber, isValidIndianPhone } from '@/lib/services/verifynow'

export async function POST(request: NextRequest) {
  try {
    const { phone, userId } = await request.json()

    // Validate input
    if (!phone || !userId) {
      return NextResponse.json(
        { error: 'Phone number and user ID are required' },
        { status: 400 }
      )
    }

    // Format and validate phone number
    const formattedPhone = formatPhoneNumber(phone)
    if (!isValidIndianPhone(formattedPhone)) {
      return NextResponse.json(
        { error: 'Invalid Indian phone number' },
        { status: 400 }
      )
    }

    // Send OTP via VerifyNow (they generate and send the OTP)
    const verifyNowService = createVerifyNowService()
    const smsResult = await verifyNowService.sendOTP(formattedPhone)

    if (smsResult.success) {
      return NextResponse.json({
        success: true,
        message: 'OTP sent successfully',
        verificationId: smsResult.verificationId,
        transactionId: smsResult.transactionId
      })
    } else {
      // Handle REQUEST_ALREADY_EXISTS error gracefully
      if (smsResult.error?.includes('REQUEST_ALREADY_EXISTS')) {
        return NextResponse.json(
          { error: 'Please wait for the current OTP to expire before requesting a new one (60 seconds)' },
          { status: 429 }
        )
      }

      return NextResponse.json(
        { error: smsResult.error || 'Failed to send OTP' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Send OTP Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
