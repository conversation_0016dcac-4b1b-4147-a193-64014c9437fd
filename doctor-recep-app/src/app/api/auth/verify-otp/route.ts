import { NextRequest, NextResponse } from "next/server"
import { createVerifyNowService } from "@/lib/services/verifynow"
import { approveUserInDatabase } from "@/lib/actions/admin-auth-actions"

export async function POST(request: NextRequest) {
  try {
    const { verificationId, otp, userId } = await request.json()

    if (!verificationId || !otp || !userId) {
      return NextResponse.json(
        { error: "Verification ID, OTP, and user ID are required" },
        { status: 400 }
      )
    }

    if (!/^\d{4}$/.test(otp)) {
      return NextResponse.json(
        { error: "OTP must be 4 digits" },
        { status: 400 }
      )
    }

    const verifyNowService = createVerifyNowService()
    const verifyResult = await verifyNowService.verifyOTP(verificationId, otp)

    // If the OTP is invalid with VerifyNow, stop here
    if (!verifyResult.success || !verifyResult.verified) {
      return NextResponse.json(
        { error: verifyResult.error || "Invalid or expired OTP" },
        { status: 400 }
      )
    }

    // OTP is valid. Now call our secure server action to do the approval
    const approvalResult = await approveUserInDatabase(userId)

    if (approvalResult.success) {
      return NextResponse.json({
        success: true,
        message: "Phone verified successfully! You can now log in."
      })
    } else {
      // If the secure action fails, return a server error
      return NextResponse.json(
        { error: approvalResult.error || "Failed to finalize verification" },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Verify OTP Error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
