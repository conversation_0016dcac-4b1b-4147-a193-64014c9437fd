import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { verifySession } from '@/lib/auth/dal'

export async function POST(request: NextRequest) {
  try {
    const session = await verifySession()
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { message } = body

    const supabase = await createClient()

    // Get doctor info and current quota
    const { data: doctor, error: doctorError } = await supabase
      .from('profiles')
      .select('name, email, clinic_name, phone, quota_used, monthly_quota')
      .eq('id', session.userId)
      .single()

    if (doctorError || !doctor) {
      return NextResponse.json(
        { success: false, error: 'Doctor not found' },
        { status: 404 }
      )
    }

    // Insert contact request into database - Enterprise pattern: Single source of truth
    const contextualMessage = [
      `Subject: Contact Founder Request`,
      `Quota at request: ${doctor.quota_used || 0}/${doctor.monthly_quota || 0}`,
      `Message: ${message || 'Doctor requested to contact founder'}`
    ].join('\n')

    const { error } = await supabase
      .from('contact_requests')
      .insert({
        doctor_id: session.userId,
        doctor_name: doctor.name,
        doctor_email: doctor.email,
        clinic_name: doctor.clinic_name || '',
        phone_number: doctor.phone || '',
        request_type: 'contact_founder',
        message: contextualMessage,
        status: 'pending'
      })

    if (error) {
      console.error('Error inserting contact request:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to send contact request' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Contact founder API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}