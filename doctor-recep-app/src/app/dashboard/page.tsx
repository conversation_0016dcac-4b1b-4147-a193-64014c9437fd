import { Metadata } from 'next'
import { Suspense } from 'react'
import { verifySession, getUser } from '@/lib/auth/dal'
import { DashboardData } from '@/components/data/dashboard-data'
import { DashboardClient } from '@/components/shared/dashboard-client'
import { PWAInstallPrompt } from '@/components/pwa/pwa-install-prompt'
import { DashboardSkeleton } from '@/components/ui/skeleton-loaders'

export const metadata: Metadata = {
  title: 'Dashboard - Celer AI',
  description: 'Create new patient consultations with AI-powered summaries',
  robots: {
    index: false,
    follow: false,
    googleBot: {
      index: false,
      follow: false,
    },
  },
}

export default async function DashboardPage() {
  // OPTIMIZED: Only verify session (fast), then stream the rest
  const [session, user] = await Promise.all([
    verifySession(),
    getUser()
  ])

  return (
    <>
      {/* STREAMING: Data loads progressively while user sees immediate structure */}
      <Suspense fallback={<DashboardSkeleton />}>
        <DashboardData doctorId={session.userId} />
      </Suspense>

      {/* Client-side components for modals */}
      <DashboardClient doctorId={session.userId} user={user} />

      {/* PWA Install Prompt */}
      <PWAInstallPrompt />
    </>
  )
}
