import { Metadata } from 'next'
import { Suspense } from 'react'
import { getAdmin } from '@/lib/auth/supabase-helpers'
import { AdminDashboardHeader } from '@/components/admin/admin-dashboard-header'
import { AdminData } from '@/components/data/admin-data'
import { AdminDashboardSkeleton } from '@/components/ui/skeleton-loaders'
import { redirect } from 'next/navigation'

export const metadata: Metadata = {
  title: 'Admin Dashboard - Celer AI',
  description: 'Administrative dashboard for managing doctors and billing',
  robots: {
    index: false,
    follow: false,
    googleBot: {
      index: false,
      follow: false,
    },
  },
}

export default async function AdminDashboardPage({
  searchParams
}: {
  searchParams: Promise<{ tab?: string }>
}) {
  // OPTIMIZED: Only verify admin (fast), then stream the rest
  const admin = await getAdmin()

  if (!admin) {
    redirect('/admin/login')
  }

  const resolvedSearchParams = await searchParams
  const activeTab = resolvedSearchParams.tab || 'doctors'

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminDashboardHeader admin={admin} />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <a
                href="/admin/dashboard?tab=doctors"
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'doctors'
                    ? 'border-teal-500 text-teal-600'
                    : 'border-transparent text-slate-800 hover:text-slate-900 hover:border-gray-300'
                }`}
              >
                Doctor Management
              </a>
              <a
                href="/admin/dashboard?tab=billing"
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'billing'
                    ? 'border-teal-500 text-teal-600'
                    : 'border-transparent text-slate-800 hover:text-slate-900 hover:border-gray-300'
                }`}
              >
                Billing & Referrals
              </a>
            </nav>
          </div>
        </div>

        {/* STREAMING: Data loads progressively while user sees immediate structure */}
        <Suspense fallback={<AdminDashboardSkeleton />}>
          <AdminData activeTab={activeTab} />
        </Suspense>
      </main>
    </div>
  )
}