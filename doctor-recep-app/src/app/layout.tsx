import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Analytics } from "@vercel/analytics/next";
import { SpeedInsights } from "@vercel/speed-insights/next";
import Script from "next/script";
import { AnalyticsProvider } from "@/components/analytics/analytics-provider";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NODE_ENV === 'production' ? 'https://www.celerai.live' : 'http://localhost:3004'),
  title: {
    default: "Celer AI - AI-Powered Medical Documentation for Indian Doctors",
    template: "%s | Celer AI"
  },
  description: "Transform patient consultations into professional medical summaries with AI. Trusted by Indian doctors for accurate, medico-legal documentation. Start your free trial today.",
  keywords: [
    "AI medical documentation",
    "patient consultation summary", 
    "healthcare AI India",
    "medical transcription",
    "clinical documentation",
    "doctor consultation notes",
    "medical AI assistant",
    "healthcare automation",
    "patient record management",
    "medical summary generator"
  ],
  authors: [{ name: "Celer AI", url: "https://celerai.live" }],
  creator: "Celer AI",
  publisher: "Celer AI",
  category: "Healthcare Technology",
  classification: "Medical Software",
  
  // Open Graph for social sharing
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://celerai.live",
    siteName: "Celer AI",
    title: "Celer AI - AI-Powered Medical Documentation",
    description: "Transform patient consultations into professional medical summaries with AI. Trusted by Indian doctors for accurate, Medico-legal documentation.",
    images: [
      {
        url: "/icons/icon-512x512.png",
        width: 512,
        height: 512,
        alt: "Celer AI - Medical Documentation Platform"
      }
    ]
  },
  
  // Twitter Card
  twitter: {
    card: "summary_large_image",
    site: "@CelerAI",
    creator: "@CelerAI",
    title: "Celer AI - AI-Powered Medical Documentation",
    description: "Transform patient consultations into professional medical summaries with AI. Trusted by Indian doctors.",
    images: ["/icons/icon-512x512.png"]
  },
  
  // Robots and indexing
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  
  // Verification and ownership
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
    other: {
      "msvalidate.01": process.env.NEXT_PUBLIC_BING_VERIFICATION || "",
    }
  },
  
  // App-specific metadata
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Celer AI",
    startupImage: "/icons/apple-touch-icon-180x180.png"
  },
  
  // Favicons and icons
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/icons/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/icons/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/icons/favicon-48x48.png", sizes: "48x48", type: "image/png" },
    ],
    apple: [
      { url: "/icons/apple-touch-icon-180x180.png", sizes: "180x180", type: "image/png" },
      { url: "/icons/apple-touch-icon-152x152.png", sizes: "152x152", type: "image/png" },
      { url: "/icons/apple-touch-icon-120x120.png", sizes: "120x120", type: "image/png" },
    ],
    other: [
      {
        rel: "mask-icon",
        url: "/icons/logo-1024x1024.png",
        color: "#14b8a6"
      }
    ]
  },
  
  // Additional metadata for healthcare compliance
  other: {
    "application-name": "Celer AI",
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default",
    "format-detection": "telephone=no",
    "theme-color": "#14b8a6",
    "color-scheme": "light",
    "supported-color-schemes": "light"
  }
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#14b8a6',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Root favicon for legacy browsers - with cache busting */}
        <link rel="icon" href="/favicon.ico?v=2" sizes="any" />
        <link rel="shortcut icon" href="/favicon.ico?v=2" />

        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />

        {/* PWA Meta Tags */}
        <meta name="theme-color" content="#14b8a6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Celer AI" />

        {/* Microsoft Tiles */}
        <meta name="msapplication-TileColor" content="#14b8a6" />
        <meta name="msapplication-TileImage" content="/icons/icon-144x144.png" />
        <meta name="msapplication-config" content="/browserconfig.xml" />

        {/* GA4 Analytics - Lazy Loaded for Performance */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-66GG02C5H5"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-66GG02C5H5', {
              page_title: document.title,
              custom_map: {'custom_parameter_1': 'zone_type'}
            });
          `}
        </Script>

        {/* Structured Data for SEO */}
        <Script
          id="structured-data"
          type="application/ld+json"
          strategy="beforeInteractive"
        >
          {`
            {
              "@context": "https://schema.org",
              "@graph": [
                {
                  "@type": "Organization",
                  "@id": "https://www.celerai.live/#organization",
                  "name": "Celer AI",
                  "url": "https://www.celerai.live",
                  "logo": {
                    "@type": "ImageObject",
                    "url": "https://www.celerai.live/icons/icon-512x512.png",
                    "width": 512,
                    "height": 512
                  },
                  "description": "AI-powered medical documentation platform for healthcare professionals",
                  "foundingDate": "2024",
                  "industry": "Healthcare Technology",
                  "knowsAbout": [
                    "Medical Documentation",
                    "Healthcare AI",
                    "Clinical Notes",
                    "Patient Consultation",
                    "Medical Transcription"
                  ],
                  "areaServed": {
                    "@type": "Country",
                    "name": "India"
                  },
                  "contactPoint": {
                    "@type": "ContactPoint",
                    "telephone": "+91-**********",
                    "contactType": "customer service",
                    "availableLanguage": ["English", "Hindi"]
                  }
                },
                {
                  "@type": "WebSite",
                  "@id": "https://www.celerai.live/#website",
                  "url": "https://www.celerai.live",
                  "name": "Celer AI",
                  "description": "AI-powered medical documentation platform for Indian doctors",
                  "publisher": {
                    "@id": "https://www.celerai.live/#organization"
                  },
                  "potentialAction": {
                    "@type": "SearchAction",
                    "target": {
                      "@type": "EntryPoint",
                      "urlTemplate": "https://www.celerai.live/blog?search={search_term_string}"
                    },
                    "query-input": "required name=search_term_string"
                  }
                },
                {
                  "@type": "SoftwareApplication",
                  "@id": "https://www.celerai.live/#software",
                  "name": "Celer AI Medical Documentation Platform",
                  "applicationCategory": "HealthApplication",
                  "operatingSystem": "Web Browser, PWA",
                  "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "INR",
                    "description": "Free trial available"
                  },
                  "featureList": [
                    "AI-powered consultation summaries",
                    "Voice-to-text transcription",
                    "Medical template generation",
                    "Enterprise grade storage",
                    "Multi-language support"
                  ],
                  "screenshot": "https://www.celerai.live/icons/icon-512x512.png",
                  "provider": {
                    "@id": "https://www.celerai.live/#organization"
                  }
                }
              ]
            }
          `}
        </Script>
      </head>
      <body className={`${inter.className} antialiased`}>
        <AnalyticsProvider>
          {children}
        </AnalyticsProvider>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}
