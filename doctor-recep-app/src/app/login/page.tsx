import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { LoginForm } from '@/components/auth/login-form'
import { GoogleOneTap } from '@/components/auth/google-one-tap'
import { GoogleFallbackButton } from '@/components/auth/google-fallback-button'
import { <PERSON><PERSON><PERSON>, Wand2, ArrowRight, Shield, Zap } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Login - Celer AI System',
  description: 'Login to your Celer AI account',
}

interface LoginPageProps {
  searchParams: Promise<{ message?: string }>
}

export default async function LoginPage({ searchParams }: LoginPageProps) {
  const resolvedSearchParams = await searchParams
  const successMessage = resolvedSearchParams.message === 'password_reset_success'
    ? 'Password updated successfully! Please sign in with your new password.'
    : null
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-10 h-10">
              <Image
                src="/icons/celer-ai-logo-navbar-40x40.png"
                alt="Celer AI - Doctor Login Portal"
                width={40}
                height={40}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/signup"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Sign Up
            </Link>
            <Link
              href="/"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Home
            </Link>
          </div>
        </div>
      </nav>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>

      {/* Main Content */}
      <div className="relative flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <h2 className="text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4 mt-16">
              Continue Your
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse relative z-10">
                 Journey
              </span>
            </h2>

            <p className="text-lg text-slate-600 mb-6">
              Sign in to create magical medical reports
            </p>

            <div className="flex justify-center items-center space-x-6 text-slate-500 text-sm">
              <div className="flex items-center space-x-1">
                <Shield className="w-4 h-4 text-indigo-600" />
                <span>Secure Login</span>
              </div>
              <div className="flex items-center space-x-1">
                <Zap className="w-4 h-4 text-emerald-500" />
                <span>AI Powered</span>
              </div>
            </div>
          </div>

          {/* Success Message */}
          {successMessage && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-green-800 text-sm font-medium">{successMessage}</span>
            </div>
          )}

          {/* Login Form Card */}
          <div className="relative">
            <div className="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse"></div>
            <div className="relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl p-8 border border-white/20">
              <LoginForm />
            </div>
          </div>

          {/* Google Sign-in Fallback */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-slate-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-gradient-to-br from-indigo-50 via-white to-cyan-50 text-slate-500">
                Or continue with
              </span>
            </div>
          </div>

          <div className="relative">
            <GoogleFallbackButton context="signin" />
          </div>

          {/* Sign up link */}
          <div className="text-center">
            <div className="bg-white/60 backdrop-blur-xl rounded-xl p-4 border border-white/30">
              <p className="text-sm text-slate-600">
                Don&apos;t have an account?{' '}
                <Link
                  href="/signup"
                  className="font-medium text-indigo-600 hover:text-purple-600 transition-colors duration-200 inline-flex items-center space-x-1"
                >
                  <span>Sign up here</span>
                  <ArrowRight className="w-3 h-3" />
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Google One Tap */}
      <GoogleOneTap context="signin" />
    </div>
  )
}