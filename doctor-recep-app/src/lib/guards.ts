/**
 * Type Guards for Role-Based Profile Types
 * 
 * Following the "Database as Muscle, Application as Brain" principle,
 * these type guards enforce business logic in the application layer
 * rather than at the database level.
 * 
 * Usage:
 * - Use isDoctorProfile() before accessing doctor-specific fields
 * - Use isAdminProfile() before accessing admin-specific logic
 * - TypeScript will narrow the types automatically after guard checks
 */

import { Profile, DoctorProfile, AdminProfile } from './types'

/**
 * Type guard to check if a profile belongs to a doctor
 * 
 * @param profile - The profile to check
 * @returns true if the profile is a doctor profile
 * 
 * @example
 * ```typescript
 * if (isDoctorProfile(profile)) {
 *   // TypeScript now knows quota_used and monthly_quota are numbers
 *   const usage = profile.quota_used / profile.monthly_quota
 * }
 * ```
 */
export function isDoctorProfile(profile: Profile): profile is DoctorProfile {
  return profile.role === 'doctor'
}

/**
 * Type guard to check if a profile belongs to an admin
 * 
 * @param profile - The profile to check
 * @returns true if the profile is an admin profile
 * 
 * @example
 * ```typescript
 * if (isAdminProfile(profile)) {
 *   // TypeScript knows this is an admin - no quota fields needed
 *   console.log(`Admin: ${profile.name}`)
 * }
 * ```
 */
export function isAdminProfile(profile: Profile): profile is AdminProfile {
  return profile.role === 'admin' || profile.role === 'super_admin'
}

/**
 * Type guard to check if a profile is a super admin specifically
 * 
 * @param profile - The profile to check
 * @returns true if the profile is a super admin
 */
export function isSuperAdminProfile(profile: Profile): profile is AdminProfile & { role: 'super_admin' } {
  return profile.role === 'super_admin'
}

/**
 * Type guard to check if a profile has quota-related fields
 * This is essentially the same as isDoctorProfile but more semantic
 * 
 * @param profile - The profile to check
 * @returns true if the profile has quota fields
 */
export function hasQuotaFields(profile: Profile): profile is DoctorProfile {
  return isDoctorProfile(profile)
}

/**
 * Type guard to check if a profile has billing-related fields
 * This is essentially the same as isDoctorProfile but more semantic
 * 
 * @param profile - The profile to check
 * @returns true if the profile has billing fields
 */
export function hasBillingFields(profile: Profile): profile is DoctorProfile {
  return isDoctorProfile(profile)
}

/**
 * Utility function to safely access quota information
 * Returns null for non-doctor profiles
 * 
 * @param profile - The profile to extract quota from
 * @returns quota info or null
 */
export function getQuotaInfo(profile: Profile): { 
  monthly_quota: number
  quota_used: number
  quota_remaining: number
  quota_percentage: number
} | null {
  if (!isDoctorProfile(profile)) {
    return null
  }
  
  const quota_remaining = profile.monthly_quota - profile.quota_used
  const quota_percentage = (profile.quota_used / profile.monthly_quota) * 100
  
  return {
    monthly_quota: profile.monthly_quota,
    quota_used: profile.quota_used,
    quota_remaining,
    quota_percentage
  }
}

/**
 * Utility function to safely access billing status
 * Returns null for non-doctor profiles
 * 
 * @param profile - The profile to extract billing status from
 * @returns billing status or null
 */
export function getBillingStatus(profile: Profile): string | null {
  if (!isDoctorProfile(profile)) {
    return null
  }
  
  return profile.billing_status
}
