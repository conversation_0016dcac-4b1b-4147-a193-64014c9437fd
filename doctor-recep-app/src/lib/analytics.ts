'use client'

/**
 * Analytics Abstraction Layer - Celer AI
 * 
 * SECURITY FRAMEWORK:
 * - Two-Zone Analytics: Public (full tracking) vs Authenticated (PII-free only)
 * - Zero PII Policy: Never send user_id, email, patient_name, or PHI to GA4
 * - Allowlist Approach: Only predefined safe events pass through
 * 
 * CTO APPROVED EVENTS FOR AUTHENTICATED ZONE:
 * - consultation_generated: Track consultation creation (no patient data)
 * - summary_approved: Track summary approval (no content)
 * - quota_warning: Track quota threshold alerts (no user data)
 * - dashboard_viewed: Track dashboard access (no personal data)
 */

// Type definitions for safe analytics events
type PublicPageEvent =
  | 'page_view'
  | 'signup_started'
  | 'signup_completed'
  | 'login_attempted'
  | 'login_successful'
  | 'password_reset_requested'
  | 'password_reset_attempted'
  | 'password_reset_failed'
  | 'password_reset_completed'
  | 'password_reset_email_sent'
  | 'blog_post_viewed'
  | 'guide_viewed'

type AuthenticatedAppEvent = 
  | 'consultation_generated'
  | 'summary_approved'
  | 'quota_warning'
  | 'dashboard_viewed'
  | 'settings_accessed'
  | 'template_updated'

type AnalyticsEvent = PublicPageEvent | AuthenticatedAppEvent

// Safe event parameters (no PII allowed)
interface SafeEventParams {
  // Only non-PII metadata allowed
  page_title?: string
  consultation_type?: string
  quota_percentage?: number
  feature_used?: string
  error_type?: string
  [key: string]: string | number | boolean | undefined
}

// Check if user is in authenticated app
function isAuthenticatedRoute(): boolean {
  if (typeof window === 'undefined') return false
  
  const authenticatedPaths = ['/dashboard', '/info', '/settings', '/templates', '/admin']
  return authenticatedPaths.some(path => window.location.pathname.startsWith(path))
}

// Allowlist of safe events for authenticated zone
const SAFE_AUTHENTICATED_EVENTS: AuthenticatedAppEvent[] = [
  'consultation_generated',
  'summary_approved', 
  'quota_warning',
  'dashboard_viewed',
  'settings_accessed',
  'template_updated'
]

// Scrub any potentially sensitive parameters
function scrubParameters(params: SafeEventParams): SafeEventParams {
  const scrubbed: SafeEventParams = {}
  
  // Only allow specific safe parameters
  const allowedParams = [
    'page_title',
    'consultation_type', 
    'quota_percentage',
    'feature_used',
    'error_type'
  ]
  
  for (const [key, value] of Object.entries(params)) {
    if (allowedParams.includes(key) && value !== undefined) {
      // Additional validation for specific params
      if (key === 'quota_percentage' && typeof value === 'number') {
        scrubbed[key] = Math.round(value) // Round to avoid precision-based identification
      } else if (typeof value === 'string' && value.length < 100) {
        scrubbed[key] = value
      } else if (typeof value === 'boolean') {
        scrubbed[key] = value
      }
    }
  }
  
  return scrubbed
}

/**
 * Main analytics tracking function
 * Enforces two-zone security model
 */
export function trackEvent(
  event: AnalyticsEvent, 
  parameters: SafeEventParams = {}
): void {
  // Only track in browser environment
  if (typeof window === 'undefined') return
  
  // Check if GA4 is loaded
  if (!isGA4Ready()) {
    console.warn('GA4 not loaded, skipping analytics event:', event)
    return
  }
  
  try {
    const isAuthenticated = isAuthenticatedRoute()
    
    if (isAuthenticated) {
      // AUTHENTICATED ZONE: Strict allowlist enforcement
      if (!SAFE_AUTHENTICATED_EVENTS.includes(event as AuthenticatedAppEvent)) {
        console.warn(`Analytics: Blocked unauthorized event in authenticated zone: ${event}`)
        return
      }
      
      // Scrub all parameters for safety
      const scrubbedParams = scrubParameters(parameters)
      
      console.log(`Analytics: Tracking safe authenticated event: ${event}`, scrubbedParams)
      window.gtag('event', event, scrubbedParams)
      
    } else {
      // PUBLIC ZONE: Full tracking allowed (but still scrub for safety)
      const scrubbedParams = scrubParameters(parameters)
      
      console.log(`Analytics: Tracking public event: ${event}`, scrubbedParams)
      window.gtag('event', event, scrubbedParams)
    }
    
  } catch (error) {
    console.error('Analytics tracking error:', error)
  }
}

/**
 * Track page views with automatic zone detection
 */
export function trackPageView(pageTitle?: string): void {
  const isAuthenticated = isAuthenticatedRoute()
  
  if (isAuthenticated) {
    // In authenticated zone, only track generic dashboard view
    trackEvent('dashboard_viewed', { page_title: 'Dashboard' })
  } else {
    // In public zone, track full page view
    trackEvent('page_view', { page_title: pageTitle })
  }
}

/**
 * Track consultation events (authenticated zone only)
 */
export function trackConsultation(type: 'generated' | 'approved', consultationType?: string): void {
  if (!isAuthenticatedRoute()) return
  
  if (type === 'generated') {
    trackEvent('consultation_generated', { 
      consultation_type: consultationType 
    })
  } else if (type === 'approved') {
    trackEvent('summary_approved', { 
      consultation_type: consultationType 
    })
  }
}

/**
 * Track quota warnings (authenticated zone only)
 */
export function trackQuotaWarning(percentage: number): void {
  if (!isAuthenticatedRoute()) return
  
  trackEvent('quota_warning', { 
    quota_percentage: percentage 
  })
}

/**
 * Track authentication events (public zone only)
 */
export function trackAuth(type: 'signup_started' | 'signup_completed' | 'login_attempted' | 'login_successful' | 'password_reset_requested' | 'password_reset_attempted' | 'password_reset_failed' | 'password_reset_completed' | 'password_reset_email_sent'): void {
  // Only track in public zone
  if (isAuthenticatedRoute()) return

  trackEvent(type)
}

/**
 * Track page load performance (authenticated zone only)
 */
export function trackPageLoadPerformance(params: { page: string; loadTime: number; componentsLoaded: number }): void {
  if (!isAuthenticatedRoute()) return

  trackEvent('dashboard_viewed', {
    page_title: params.page,
    feature_used: 'page_load_performance'
  })
}

/**
 * Track search performance (authenticated zone only)
 */
export function trackSearchPerformance(params: { searchTerm: string; resultsCount: number; queryTime: number; ftsUsed: boolean }): void {
  if (!isAuthenticatedRoute()) return

  trackEvent('dashboard_viewed', {
    feature_used: 'search_performance'
  })
}

/**
 * Track background preload performance (authenticated zone only)
 */
export function trackBackgroundPreload(params: { scrollPercentage: number; itemsPreloaded: number }): void {
  if (!isAuthenticatedRoute()) return

  trackEvent('dashboard_viewed', {
    feature_used: 'background_preload'
  })
}

/**
 * Check if GA4 is ready and loaded
 */
function isGA4Ready(): boolean {
  return typeof window !== 'undefined' && 
         typeof window.gtag === 'function' &&
         typeof window.dataLayer !== 'undefined'
}

/**
 * Wait for GA4 to be ready with timeout
 */
function waitForGA4Ready(callback: () => void, timeout = 10000): void {
  if (isGA4Ready()) {
    callback()
    return
  }

  const startTime = Date.now()
  const checkInterval = setInterval(() => {
    if (isGA4Ready()) {
      clearInterval(checkInterval)
      callback()
    } else if (Date.now() - startTime > timeout) {
      clearInterval(checkInterval)
      console.warn('GA4 failed to load within timeout, proceeding without analytics')
    }
  }, 100)
}

/**
 * Initialize analytics with environment detection
 */
export function initializeAnalytics(): void {
  if (typeof window === 'undefined') return

  // Log analytics initialization
  console.log('Analytics initialized with two-zone security model')

  // Wait for GA4 to be ready before tracking
  waitForGA4Ready(() => {
    trackPageView(document.title)
  })
}

// Global gtag function type declaration
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js',
      targetId: string | AnalyticsEvent,
      config?: any
    ) => void
    dataLayer: any[]
  }
}
