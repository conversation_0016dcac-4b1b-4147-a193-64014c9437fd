import 'server-only'
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/lib/types'

/**
 * Create a Supabase admin client with service role key
 * This client bypasses RLS and has full database access
 * ONLY use this in secure server-side contexts
 */
export function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceRoleKey) {
    throw new Error('Missing Supabase environment variables for admin client')
  }

  return createClient<Database>(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}
