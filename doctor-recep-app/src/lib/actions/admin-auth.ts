'use server'

import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { AdminLoginFormSchema } from '@/lib/validations'
import { FormState, UserRole } from '@/lib/types'

export async function adminLogin(state: FormState, formData: FormData): Promise<FormState> {
  // Validate form fields
  const validatedFields = AdminLoginFormSchema.safeParse({
    email: formData.get('email'),
    password: formData.get('password'),
  })

  // If any form fields are invalid, return early
  if (!validatedFields.success) {
    return {
      success: false,
      message: 'Invalid form fields.',
    }
  }

  const { email, password } = validatedFields.data

  try {
    const supabase = await createClient()

    // Sign in with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (authError || !authData.user) {
      console.error('Supabase admin auth error:', authError)
      return {
        success: false,
        message: 'Invalid email or password.',
      }
    }

    // Check if user has admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', authData.user.id)
      .single()

    if (profileError || !profile || !['admin', 'super_admin'].includes(profile.role)) {
      // Sign out the user since they're not an admin
      await supabase.auth.signOut()
      return {
        success: false,
        message: 'Access denied. Admin privileges required.',
      }
    }

    // User is authenticated and has admin role - Supabase handles the session
    // No need to return success, just redirect directly
  } catch (error) {
    console.error("Admin Login Error:", error);
    return {
      success: false,
      message: 'An unexpected error occurred.',
    }
  }

  // If execution reaches here, login was successful
  redirect('/admin/dashboard')
}

export async function adminLogout() {
  const supabase = await createClient()
  await supabase.auth.signOut()
  redirect('/admin/login')
}

export async function createAdminUser(
  email: string,
  password: string,
  name: string,
  role: 'admin' | 'super_admin' = 'admin'
): Promise<{ success: boolean; error?: string; adminId?: string }> {
  try {
    const supabase = await createClient()

    // DEPRECATED: This function is deprecated in favor of the CLI script
    // Use: npm run create-admin instead
    console.warn('⚠️  createAdminUser function is deprecated. Use CLI script: npm run create-admin')

    // Create admin user with Supabase Auth using the new metadata approach
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      user_metadata: {
        name,
        role // The trigger will read this and set the role appropriately
      },
      email_confirm: true
    })

    if (authError || !authData.user) {
      console.error('Supabase admin creation error:', authError)
      return {
        success: false,
        error: authError?.message || 'Failed to create admin user.',
      }
    }

    // The handle_new_user trigger has already created the profile
    // Now update it to NULL out doctor-specific fields for admins
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        approved: null as any, // NULL = admin-only access, true = doctor access, false = pending
        approved_at: new Date().toISOString(),
        // NULL out all doctor-specific fields for admin users
        monthly_quota: null,
        quota_used: null,
        quota_reset_at: null,
        billing_status: null,
        available_discount_amount: null,
        referral_discount_earned: null,
        total_referrals: null,
        successful_referrals: null,
        clinic_name: null,
        phone: null,
        referral_code: null,
        referred_by: null,
        trial_ends_at: null,
        next_billing_date: null,
        last_payment_date: null,
        current_plan_id: null,
        conversion_date: null
      })
      .eq('id', authData.user.id)

    if (updateError) {
      console.error('Profile update error:', updateError)
      return {
        success: false,
        error: 'Failed to finalize admin profile.',
      }
    }

    return {
      success: true,
      adminId: authData.user.id,
    }
  } catch (error) {
    console.error('Create admin error:', error)
    return {
      success: false,
      error: 'An unexpected error occurred.',
    }
  }
}