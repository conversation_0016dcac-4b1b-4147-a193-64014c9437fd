'use server'

import { createClient } from '@/lib/supabase/server'
import { ApiResponse, FormState, ContactRequestType, ContactRequestStatus, ContactRequest } from '@/lib/types'
import { revalidatePath } from 'next/cache'

// Re-export ContactRequest for other files to use
export type { ContactRequest }

export async function createContactRequest(
  doctorId: string,
  message?: string,
  subject?: string,
  requestType?: string
): Promise<ApiResponse<string>> {
  try {
    console.log('Creating contact request for doctorId:', doctorId)
    const supabase = await createClient()

    // Get doctor information first
    console.log('Fetching doctor info...')
    const { data: doctor, error: doctorError } = await supabase
      .from('profiles')
      .select('name, email, clinic_name, phone, quota_used, monthly_quota')
      .eq('id', doctorId)
      .single()

    console.log('Doctor fetch result:', { doctor, doctorError })

    if (doctorError || !doctor) {
      console.error('Doctor not found:', { doctorId, doctor<PERSON><PERSON>r })
      return { success: false, error: `Doctor not found: ${doctorError?.message || 'No doctor data'}` }
    }

    // Enterprise pattern: Single source of truth - quota data stays in profiles table
    // Store context in message field with structured format for admin visibility
    const contextualMessage = [
      `Subject: ${subject || 'General Inquiry'}`,
      `Quota at request: ${doctor.quota_used || 0}/${doctor.monthly_quota || 0}`,
      `Message: ${message || 'Contact request from dashboard'}`
    ].join('\n')

    const insertData = {
      doctor_id: doctorId,
      doctor_name: doctor.name,
      doctor_email: doctor.email,
      clinic_name: doctor.clinic_name || '',
      phone_number: doctor.phone || '',
      request_type: (requestType || 'general') as ContactRequestType,
      message: contextualMessage
    }
    
    console.log('Creating contact request with data:', insertData)

    const { data, error } = await supabase
      .from('contact_requests')
      .insert(insertData)
      .select('id')
      .single()

    console.log('Insert result:', { data, error })

    if (error) {
      console.error('Failed to create contact request:', error)
      return { success: false, error: `Database error: ${error.message}` }
    }

    // Force revalidation of admin paths
    revalidatePath('/admin/dashboard')
    revalidatePath('/admin')
    
    console.log('Contact request created successfully with ID:', data.id)
    
    return { success: true, data: data.id }
  } catch (error) {
    console.error('Unexpected error creating contact request:', error)
    return { success: false, error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` }
  }
}

export async function getContactRequests(): Promise<ApiResponse<ContactRequest[]>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('contact_requests')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Database error fetching contact requests:', error)
      return { success: false, error: 'Failed to fetch contact requests' }
    }

    // Only log if this is called with explicit debug flag or if there are new requests
    if (process.env.NODE_ENV === 'development') {
      console.log('Fetched contact requests:', {
        count: data?.length || 0,
        pending: data?.filter(r => r.status === 'pending').length || 0
      })
    }

    return { success: true, data: data || [] }
  } catch (error) {
    console.error('Error fetching contact requests:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function getPendingContactRequests(): Promise<ApiResponse<ContactRequest[]>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('contact_requests')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })

    if (error) {
      return { success: false, error: 'Failed to fetch pending contact requests' }
    }

    return { success: true, data: data || [] }
  } catch (error) {
    console.error('Error fetching pending contact requests:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function updateContactRequestStatus(
  requestId: string,
  status: 'pending' | 'contacted' | 'resolved'
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = await createClient()

    const { error } = await supabase
      .from('contact_requests')
      .update({ status })
      .eq('id', requestId)

    if (error) {
      return { success: false, error: 'Failed to update contact request status' }
    }

    revalidatePath('/admin/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Error updating contact request status:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function getContactRequestsCount(): Promise<ApiResponse<{
  total: number;
  pending: number;
  contacted: number;
  resolved: number;
}>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('contact_requests')
      .select('status')

    if (error) {
      return { success: false, error: 'Failed to fetch contact requests count' }
    }

    const counts = {
      total: data?.length || 0,
      pending: data?.filter(r => r.status === 'pending').length || 0,
      contacted: data?.filter(r => r.status === 'contacted').length || 0,
      resolved: data?.filter(r => r.status === 'resolved').length || 0
    }

    return { success: true, data: counts }
  } catch (error) {
    console.error('Error fetching contact requests count:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function hasActiveContactRequest(doctorId: string): Promise<ApiResponse<boolean>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('contact_requests')
      .select('id')
      .eq('doctor_id', doctorId)
      .eq('status', 'pending')
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      return { success: false, error: 'Failed to check contact request status' }
    }

    return { success: true, data: !!data }
  } catch (error) {
    console.error('Error checking contact request status:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

/**
 * Create a manual approval request when user fails OTP verification
 * This implements the human-in-the-loop fallback for phone verification
 */
export async function requestManualApproval(userId: string): Promise<FormState> {
  if (!userId) {
    return { success: false, message: 'User ID is missing.' }
  }

  try {
    const supabase = await createClient()

    // Fetch the user's details to populate the request
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('name, email, clinic_name, phone')
      .eq('id', userId)
      .single()

    if (profileError || !userProfile) {
      return { success: false, message: 'Could not find user profile to create request.' }
    }

    // Insert a new contact request with a specific type
    const { error: insertError } = await supabase.from('contact_requests').insert({
      doctor_id: userId,
      doctor_name: userProfile.name,
      doctor_email: userProfile.email,
      clinic_name: userProfile.clinic_name,
      phone_number: userProfile.phone,
      request_type: 'manual_approval_request', // Special type for filtering
      message: 'User requested manual account approval due to a potential OTP verification issue.',
      status: 'pending',
    })

    if (insertError) {
      console.error('Manual approval request creation error:', insertError)
      return { success: false, message: 'Failed to create manual approval request.' }
    }

    // The existing webhook on this table will notify the admin dashboard automatically.
    // No need to revalidate path here as it doesn't affect user-facing content immediately.

    return { success: true, message: 'Request sent successfully!' }
  } catch (error) {
    console.error('Unexpected error in requestManualApproval:', error)
    return { success: false, message: 'An unexpected error occurred.' }
  }
}