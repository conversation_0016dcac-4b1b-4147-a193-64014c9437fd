'use server'

import { revalidatePath } from 'next/cache'
import { verifySession } from '@/lib/auth/dal'
import { ApiResponse } from '@/lib/types'

export async function updateDoctorSettings(
  doctorId: string
): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session || session.userId !== doctorId) {
      return { success: false, error: 'Unauthorized' }
    }

    // Since template config is removed, just return success
    revalidatePath('/settings')
    revalidatePath('/dashboard')

    return { success: true, data: true }
  } catch (error) {
    console.error('Update settings error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}
