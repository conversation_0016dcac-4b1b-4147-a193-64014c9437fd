'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/lib/supabase/server'
import { verifySession } from '@/lib/auth/dal'
import { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'
import { ApiResponse, Consultation, Database } from '@/lib/types'
import { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'

// Helper to parse a Json | null array field from Supabase
function parseJsonStringArray(field: unknown): string[] {
  if (!field) return []
  if (Array.isArray(field)) return field as string[]
  if (typeof field === 'string') {
    try {
      return JSON.parse(field)
    } catch {
      return []
    }
  }
  return []
}

// New function to handle file uploads and consultation creation
export async function createConsultationWithFiles(
  audioFile: File,
  imageFiles: File[],
  additionalAudioFiles: File[],
  submittedBy: 'doctor' | 'receptionist',
  doctorNotes?: string,
  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',
  patientName?: string
): Promise<ApiResponse<Consultation>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    // Validate total file size
    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]
    const totalSizeValidation = validateTotalSize(allFiles)
    if (!totalSizeValidation.valid) {
      return { success: false, error: totalSizeValidation.error! }
    }

    // Generate consultation ID for file organization
    const consultationId = crypto.randomUUID()

    // Upload primary audio file
    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')
    if (!audioUploadResult.success) {
      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }
    }

    // Upload additional audio files
    let additionalAudioUrls: string[] = []
    if (additionalAudioFiles.length > 0) {
      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')
      if (!additionalAudioResult.success) {
        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }
      }
      additionalAudioUrls = additionalAudioResult.urls || []
    }

    // Upload image files
    let imageUrls: string[] = []
    if (imageFiles.length > 0) {
      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')
      if (!imageUploadResult.success) {
        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }
      }
      imageUrls = imageUploadResult.urls || []
    }

    const totalFileSize = calculateTotalFileSize(allFiles)

    const supabase = await createClient()

    // Insert consultation with pre-generated ID
    const { data: consultation, error } = await supabase
      .from('consultations')
      .insert({
        id: consultationId,
        doctor_id: session.userId,
        primary_audio_url: audioUploadResult.url!,
        additional_audio_urls: additionalAudioUrls,
        image_urls: imageUrls,
        submitted_by: submittedBy,
        status: 'pending_generation',
        total_file_size_bytes: totalFileSize,
        doctor_notes: doctorNotes || null,
        consultation_type: consultationType,
        patient_name: patientName || null,
      } as Database['public']['Tables']['consultations']['Insert'])
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return { success: false, error: 'Failed to create consultation' }
    }

    revalidatePath('/dashboard')
    revalidatePath('/mobile')

    return { success: true, data: consultation as Consultation }
  } catch (error) {
    console.error('Create consultation with files error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Update consultation with generated summary
    const { error: updateError } = await supabase
      .from('consultations')
      .update({
        ai_generated_note: summary,
        status: 'generated',
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations

    if (updateError) {
      console.error('Update error:', updateError)
      return { success: false, error: 'Failed to save generated summary' }
    }

    revalidatePath('/dashboard')

    return { success: true, data: summary }
  } catch (error) {
    console.error('Save streaming summary error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    // Extract URLs from form data
    const primaryAudioUrl = formData.get('primary_audio_url') as string
    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string
    const imageUrlsString = formData.get('image_urls') as string
    const totalFileSizeString = formData.get('total_file_size_bytes') as string

    let additionalAudioUrls: string[] = []
    let imageUrls: string[] = []
    let totalFileSize = 0

    // Parse additional audio URLs
    if (additionalAudioUrlsString) {
      try {
        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)
      } catch (error) {
        console.error('Error parsing additional_audio_urls:', error)
      }
    }

    // Parse image URLs
    if (imageUrlsString) {
      try {
        imageUrls = JSON.parse(imageUrlsString)
      } catch (error) {
        console.error('Error parsing image_urls:', error)
      }
    }

    // Parse total file size
    if (totalFileSizeString) {
      totalFileSize = parseInt(totalFileSizeString, 10) || 0
    }

    // Validate form data
    const validatedFields = ConsultationCreateSchema.safeParse({
      primary_audio_url: primaryAudioUrl,
      additional_audio_urls: additionalAudioUrls,
      image_urls: imageUrls,
      submitted_by: formData.get('submitted_by'),
      total_file_size_bytes: totalFileSize,
    })

    if (!validatedFields.success) {
      return {
        success: false,
        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)
      }
    }

    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data

    const supabase = await createClient()

    // Insert consultation
    const { data: consultation, error } = await supabase
      .from('consultations')
      .insert({
        doctor_id: session.userId,
        primary_audio_url,
        additional_audio_urls: additional_audio_urls || [],
        image_urls: image_urls || [],
        submitted_by,
        status: 'pending_generation',
        total_file_size_bytes: total_file_size_bytes || 0,
      } as Database['public']['Tables']['consultations']['Insert'])
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return { success: false, error: 'Failed to create consultation' }
    }

    revalidatePath('/dashboard')
    revalidatePath('/mobile')

    return { success: true, data: consultation as Consultation }
  } catch (error) {
    console.error('Create consultation error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// OPTIMIZED: Paginated consultations with FTS search support
export async function getConsultations({
  page = 1,
  pageSize = 15, // Start with small initial load
  status,
  searchTerm
}: {
  page?: number
  pageSize?: number
  status?: 'pending_generation' | 'generated' | 'approved'
  searchTerm?: string
} = {}): Promise<ApiResponse<{
  consultations: Consultation[]
  hasMore: boolean
  totalCount?: number
}>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()
    const from = (page - 1) * pageSize
    const to = from + pageSize - 1

    // Performance tracking: Start timer
    const queryStartTime = performance.now()

    let query = supabase
      .from('consultations')
      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })
      .eq('doctor_id', session.userId)
      .order('created_at', { ascending: false })
      .range(from, to)

    // Apply status filter
    if (status) {
      query = query.eq('status', status)
    }

    // Apply FTS search if provided with prefix matching for fuzzy search
    const isSearchQuery = searchTerm && searchTerm.trim()
    if (isSearchQuery) {
      // Convert search term to FTS query format with prefix matching
      // This enables partial matching: "sid" will match "Siddharth"
      const ftsQuery = searchTerm.trim().split(/\s+/).map(term => `${term}:*`).join(' & ')
      query = query.textSearch('fts', ftsQuery)
    }

    const { data: consultations, error, count } = await query

    // Performance tracking: Calculate query time
    const queryTime = performance.now() - queryStartTime

    // Track search performance if this was a search query
    if (isSearchQuery && typeof window !== 'undefined') {
      // Import analytics dynamically to avoid SSR issues
      import('@/lib/analytics').then(({ trackSearchPerformance }) => {
        trackSearchPerformance({
          searchTerm: searchTerm.trim(),
          resultsCount: consultations?.length || 0,
          queryTime: Math.round(queryTime),
          ftsUsed: true
        })
      }).catch(() => {
        // Silently fail analytics tracking
      })
    }

    if (error) {
      console.error('Database error:', error)

      // Handle "Requested range not satisfiable" error gracefully
      if (error.code === 'PGRST103') {
        return {
          success: true,
          data: {
            consultations: [],
            hasMore: false,
            totalCount: count || 0
          }
        }
      }

      return { success: false, error: 'Failed to fetch consultations' }
    }

    // Map to Consultation[]
    const typedConsultations = (consultations || []).map((row: any) => {
      return {
        id: row.id,
        doctor_id: row.doctor_id!,
        submitted_by: row.submitted_by as 'doctor' | 'receptionist',
        primary_audio_url: row.primary_audio_url,
        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),
        image_urls: parseJsonStringArray(row.image_urls),
        ai_generated_note: row.ai_generated_note ?? undefined,
        edited_note: row.edited_note ?? undefined,
        status: row.status as 'pending_generation' | 'generated' | 'approved',
        patient_number: row.patient_number ?? undefined,
        patient_name: row.patient_name ?? undefined,
        total_file_size_bytes: row.total_file_size_bytes ?? 0,
        file_retention_until: row.file_retention_until,
        created_at: row.created_at,
        updated_at: row.updated_at,
        consultation_type: row.consultation_type ?? 'outpatient',
        doctor_notes: row.doctor_notes ?? undefined,
        additional_notes: row.additional_notes ?? undefined,
      } as Consultation
    })

    const hasMore = (count || 0) > to + 1

    return {
      success: true,
      data: {
        consultations: typedConsultations,
        hasMore,
        totalCount: count || 0
      }
    }
  } catch (error) {
    console.error('Get consultations error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}



export async function generateSummaryStream(
  consultationId: string,
  additionalImages?: string[],
  onChunk?: (chunk: string) => void,
  onComplete?: (summary: string) => void,
  onError?: (error: string) => void,
  consultationType?: string,
  doctorNotes?: string,
  additionalNotes?: string
): Promise<ApiResponse<string>> {
  try {
    const session = await verifySession()
    if (!session) {
      onError?.('Unauthorized')
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Get consultation and doctor data
    const { data: consultation, error: consultationError } = await supabase
      .from('consultations')
      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, profiles(name)')
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)
      .single()

    if (consultationError || !consultation) {
      onError?.('Consultation not found')
      return { success: false, error: 'Consultation not found' }
    }

    // Strictly type consultation
    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { profiles: { name: string } }
    const typedConsultation = consultation as ConsultationRow

    // Parse additional_audio_urls and image_urls from JSON if needed
    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)
    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)
    const allImageUrls = [...imageUrls, ...(additionalImages || [])]

    // Use the SAME production-grade streaming approach as the Create buttons
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        primary_audio_url: typedConsultation.primary_audio_url,
        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],
        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],
        submitted_by: typedConsultation.submitted_by || 'doctor',
        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',
        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,
        additional_notes: additionalNotes || undefined,
        patient_name: typedConsultation.patient_name || undefined,
        doctor_name: typedConsultation.profiles.name || undefined,
        created_at: typedConsultation.created_at || undefined,
      }),
    })

    if (!response.ok) {
      const errorMsg = `HTTP ${response.status}: ${response.statusText}`
      onError?.(errorMsg)
      return { success: false, error: errorMsg }
    }

    const reader = response.body?.getReader()
    if (!reader) {
      onError?.('No reader available')
      return { success: false, error: 'No reader available' }
    }

    const decoder = new TextDecoder()
    let fullSummary = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.type === 'chunk' && data.text) {
                // Format text with proper line breaks (same as production)
                const formattedText = data.text.replace(/\\n/g, '\n').replace(/\n\n+/g, '\n\n')
                fullSummary += formattedText
                // Call onChunk with the new text chunk
                onChunk?.(formattedText)
              }
            } catch (_e) {
              // Ignore parse errors (same as production)
            }
          }
        }
      }

      // Call onComplete with final summary
      onComplete?.(fullSummary)

      // Save the streamed summary using the same approach as production
      try {
        const saveResult = await saveStreamingSummary(consultationId, fullSummary)
        if (!saveResult.success) {
          console.error('Failed to save streaming summary:', saveResult.error)
          onError?.(`Summary generated but failed to save: ${saveResult.error}`)
          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }
        }
      } catch (saveError) {
        console.error('Error saving streaming summary:', saveError)
        onError?.('Summary generated but failed to save. Please try regenerate.')
        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }
      }

      revalidatePath('/dashboard')
      return { success: true, data: fullSummary }

    } catch (error) {
      console.error('❌ Generate error:', error)
      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`
      onError?.(errorMsg)
      return { success: false, error: errorMsg }
    }

  } catch (error) {
    console.error('Generate streaming summary error:', error)
    onError?.('An unexpected error occurred')
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function approveConsultation(
  consultationId: string,
  editedNote: string
): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    // Validate edited note
    const validatedFields = ConsultationUpdateSchema.safeParse({
      edited_note: editedNote,
    })

    if (!validatedFields.success) {
      return { success: false, error: 'Invalid note content' }
    }

    const supabase = await createClient()

    // Update consultation
    const { error } = await supabase
      .from('consultations')
      .update({
        edited_note: editedNote,
        status: 'approved',
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (error) {
      console.error('Update error:', error)
      return { success: false, error: 'Failed to approve consultation' }
    }

    revalidatePath('/dashboard')

    return { success: true, data: true }
  } catch (error) {
    console.error('Approve consultation error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Update consultation with new image URLs
    const { error } = await supabase
      .from('consultations')
      .update({
        image_urls: imageUrls,
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (error) {
      console.error('Update consultation images error:', error)
      return { success: false, error: 'Failed to update consultation images' }
    }

    revalidatePath('/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Update consultation images error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }
    const supabase = await createClient()

    // Fetch the consultation
    const { data: consultation, error: fetchError } = await supabase
      .from('consultations')
      .select('additional_audio_urls, status')
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)
      .single()
    if (fetchError || !consultation) {
      return { success: false, error: 'Consultation not found' }
    }
    if (consultation.status === 'approved') {
      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }
    }

    // Upload the additional audio file
    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')
    if (!uploadResult.success) {
      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }
    }

    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)
    additionalAudioUrls.push(uploadResult.url!)

    // Update the consultation
    const { error: updateError } = await supabase
      .from('consultations')
      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
    if (updateError) {
      return { success: false, error: 'Failed to add additional audio' }
    }
    revalidatePath('/dashboard')
    revalidatePath('/record')
    return { success: true, data: true }
  } catch (error) {
    console.error('Add additional audio error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }
    const supabase = await createClient()

    // Fetch the consultation
    const { data: consultation, error: fetchError } = await supabase
      .from('consultations')
      .select('image_urls, status')
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)
      .single()
    if (fetchError || !consultation) {
      return { success: false, error: 'Consultation not found' }
    }
    if (consultation.status === 'approved') {
      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }
    }

    // Upload all image files
    const uploadResults = await Promise.all(
      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))
    )

    // Check if all uploads succeeded
    const failedUploads = uploadResults.filter(result => !result.success)
    if (failedUploads.length > 0) {
      const errors = failedUploads.map(f => f.error).join(', ')
      return { success: false, error: `Image upload failed: ${errors}` }
    }

    // Get existing image URLs and add new ones
    const existingImageUrls = parseJsonStringArray(consultation.image_urls)
    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)
    const allImageUrls = [...existingImageUrls, ...newImageUrls]

    // Update the consultation
    const { error: updateError } = await supabase
      .from('consultations')
      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
    if (updateError) {
      return { success: false, error: 'Failed to add additional images' }
    }

    revalidatePath('/dashboard')
    revalidatePath('/record')
    return { success: true, data: true }
  } catch (error) {
    console.error('Add additional images error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Update consultation type
    const { error } = await supabase
      .from('consultations')
      .update({
        consultation_type: consultationType,
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (error) {
      console.error('Update consultation type error:', error)
      return { success: false, error: 'Failed to update consultation type' }
    }

    revalidatePath('/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Update consultation type error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Update consultation with edited note
    const { error } = await supabase
      .from('consultations')
      .update({
        edited_note: editedNote,
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (error) {
      console.error('Save edited note error:', error)
      return { success: false, error: 'Failed to save edited note' }
    }

    revalidatePath('/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Save edited note error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Update consultation with additional notes
    const { error } = await supabase
      .from('consultations')
      .update({
        additional_notes: additionalNotes || null,
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (error) {
      console.error('Update additional notes error:', error)
      return { success: false, error: 'Failed to update additional notes' }
    }

    revalidatePath('/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Update additional notes error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Update consultation with patient name
    const { error } = await supabase
      .from('consultations')
      .update({
        patient_name: patientName || null,
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (error) {
      console.error('Update patient name error:', error)
      return { success: false, error: 'Failed to update patient name' }
    }

    revalidatePath('/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Update patient name error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Clear edited note from consultation
    const { error } = await supabase
      .from('consultations')
      .update({
        edited_note: null,
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (error) {
      console.error('Clear edited note error:', error)
      return { success: false, error: 'Failed to clear edited note' }
    }

    revalidatePath('/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Clear edited note error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// Delete additional audio from consultation
export async function deleteAdditionalAudio(
  consultationId: string,
  audioUrl: string
): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Get current consultation
    const { data: consultation, error: fetchError } = await supabase
      .from('consultations')
      .select('additional_audio_urls, status')
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)
      .single()

    if (fetchError || !consultation) {
      return { success: false, error: 'Consultation not found' }
    }

    // Check if consultation can be modified
    if (consultation.status === 'approved') {
      return { success: false, error: 'Cannot delete files from approved consultations' }
    }

    // Parse and filter out the audio URL
    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)
    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)

    // Update consultation
    const { error: updateError } = await supabase
      .from('consultations')
      .update({
        additional_audio_urls: updatedAudioUrls,
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (updateError) {
      console.error('Delete additional audio error:', updateError)
      return { success: false, error: 'Failed to delete additional audio' }
    }

    // Delete file from storage
    try {
      const { deleteFile } = await import('@/lib/storage')
      const filePath = audioUrl.split('/').pop() || ''
      await deleteFile(filePath, 'audio')
    } catch (storageError) {
      console.error('Storage deletion error:', storageError)
      // Don't fail the operation if storage deletion fails
    }

    revalidatePath('/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Delete additional audio error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// Delete image from consultation
export async function deleteConsultationImage(
  consultationId: string,
  imageUrl: string
): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Get current consultation
    const { data: consultation, error: fetchError } = await supabase
      .from('consultations')
      .select('image_urls, status')
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)
      .single()

    if (fetchError || !consultation) {
      return { success: false, error: 'Consultation not found' }
    }

    // Check if consultation can be modified
    if (consultation.status === 'approved') {
      return { success: false, error: 'Cannot delete files from approved consultations' }
    }

    // Parse and filter out the image URL
    const imageUrls = parseJsonStringArray(consultation.image_urls)
    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)

    // Update consultation
    const { error: updateError } = await supabase
      .from('consultations')
      .update({
        image_urls: updatedImageUrls,
      } as Database['public']['Tables']['consultations']['Update'])
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (updateError) {
      console.error('Delete consultation image error:', updateError)
      return { success: false, error: 'Failed to delete image' }
    }

    // Delete file from storage
    try {
      const { deleteFile } = await import('@/lib/storage')
      const filePath = imageUrl.split('/').pop() || ''
      await deleteFile(filePath, 'image')
    } catch (storageError) {
      console.error('Storage deletion error:', storageError)
      // Don't fail the operation if storage deletion fails
    }

    revalidatePath('/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Delete consultation image error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// Get consultation statistics using optimized database view
export async function getConsultationStats(): Promise<ApiResponse<{
  total_consultations: number
  pending_consultations: number
  approved_consultations: number
  today_consultations: number
}>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Get consultation stats with direct queries (REVERTED - view not ready)
    const { data: consultations, error } = await supabase
      .from('consultations')
      .select('status, created_at')
      .eq('doctor_id', session.userId)

    if (error) {
      console.error('Database error:', error)
      return { success: false, error: 'Failed to fetch consultation stats' }
    }

    const today = new Date().toISOString().split('T')[0]
    const stats = {
      total_consultations: consultations?.length || 0,
      pending_consultations: consultations?.filter(c => c.status === 'pending_generation').length || 0,
      approved_consultations: consultations?.filter(c => c.status === 'approved').length || 0,
      today_consultations: consultations?.filter(c => c.created_at.startsWith(today)).length || 0
    }

    return {
      success: true,
      data: {
        total_consultations: Number(stats.total_consultations),
        pending_consultations: Number(stats.pending_consultations),
        approved_consultations: Number(stats.approved_consultations),
        today_consultations: Number(stats.today_consultations)
      }
    }
  } catch (error) {
    console.error('Get consultation stats error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}
