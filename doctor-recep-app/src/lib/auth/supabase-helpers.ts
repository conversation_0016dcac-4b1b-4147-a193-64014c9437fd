import 'server-only'
import { cache } from 'react'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { Profile } from '@/lib/types'

/**
 * Verify user session and get authenticated user
 * Redirects to login if not authenticated
 */
export const verifySession = cache(async () => {
  const supabase = await createClient()
  
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    redirect('/login')
  }
  
  return { isAuth: true, userId: user.id, user }
})

/**
 * Check session without redirecting
 * Returns null if not authenticated
 */
export const checkSession = cache(async () => {
  const supabase = await createClient()
  
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    return null
  }
  
  return { isAuth: true, userId: user.id, user }
})

/**
 * Get user profile from the profiles table
 * Requires authenticated session
 */
export const getUser = cache(async (): Promise<Profile | null> => {
  const session = await verifySession()
  if (!session) return null

  try {
    const supabase = await createClient()
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.userId)
      .single()

    if (error) {
      console.error('Failed to fetch user profile:', error.message || error)
      return null
    }

    return profile
  } catch (error) {
    console.error('Failed to fetch user profile:', error instanceof Error ? error.message : error)
    return null
  }
})

/**
 * Get user profile without requiring authentication
 * Returns null if not found or not authenticated
 */
export const getUserOptional = cache(async (): Promise<Profile | null> => {
  const session = await checkSession()
  if (!session) return null

  try {
    const supabase = await createClient()
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.userId)
      .single()

    if (error) {
      console.error('Failed to fetch user profile:', error.message || error)
      return null
    }

    return profile
  } catch (error) {
    console.error('Failed to fetch user profile:', error instanceof Error ? error.message : error)
    return null
  }
})

/**
 * Verify admin session
 * Redirects to admin login if not authenticated or not admin
 */
export const verifyAdminSession = cache(async () => {
  const session = await verifySession()
  
  const supabase = await createClient()
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', session.userId)
    .single()

  if (error || !profile || !['admin', 'super_admin'].includes(profile.role)) {
    redirect('/admin/login')
  }

  return { 
    isAuth: true, 
    adminId: session.userId, 
    role: profile.role as 'admin' | 'super_admin' 
  }
})

/**
 * Check if current user has admin privileges
 * Returns false if not authenticated or not admin
 */
export const checkAdminAccess = cache(async (): Promise<boolean> => {
  try {
    const session = await checkSession()
    if (!session) return false

    const supabase = await createClient()
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', session.userId)
      .single()

    if (error || !profile) return false

    return ['admin', 'super_admin'].includes(profile.role)
  } catch {
    return false
  }
})

/**
 * Get admin profile
 * Requires admin authentication
 */
export const getAdmin = cache(async (): Promise<Profile | null> => {
  const adminSession = await verifyAdminSession()
  if (!adminSession) return null

  try {
    const supabase = await createClient()
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', adminSession.adminId)
      .single()

    if (error) {
      console.error('Failed to fetch admin profile:', error.message || error)
      return null
    }

    return profile
  } catch (error) {
    console.error('Failed to fetch admin profile:', error instanceof Error ? error.message : error)
    return null
  }
})

/**
 * Sign out user
 */
export async function signOut() {
  const supabase = await createClient()
  await supabase.auth.signOut()
  redirect('/login')
}

/**
 * Sign out admin
 */
export async function signOutAdmin() {
  const supabase = await createClient()
  await supabase.auth.signOut()
  redirect('/admin/login')
}
