import 'server-only'
import { createClient } from '@/lib/supabase/server'
import { FormState } from '@/lib/types'

/**
 * DEPRECATED: This function has been replaced by approveUserInDatabase in admin-auth-actions.ts
 * The old implementation had a security flaw - it tried to use admin functions with a regular client.
 *
 * @deprecated Use approveUserInDatabase from @/lib/actions/admin-auth-actions instead
 */

/**
 * Create a new user profile after Supabase Auth signup
 * This is called from the signup action after successful auth.signUp
 */
export async function createUserProfile(
  userId: string,
  email: string,
  name: string,
  clinicName?: string,
  phone?: string,
  referredBy?: string
): Promise<FormState> {
  try {
    const supabase = await createClient()

    // Generate a unique referral code
    const referralCode = `REF${Math.random().toString(36).substring(2, 8).toUpperCase()}`

    // NOTE: The handle_new_user trigger creates a basic profile
    // We need to UPDATE it with doctor-specific information, not INSERT
    const { error } = await supabase
      .from('profiles')
      .update({
        clinic_name: clinicName || null,
        phone: phone || null,
        role: 'doctor',
        approved: false, // Will be set to true after phone verification
        referral_code: referralCode,
        referred_by: referredBy || null,
        // Doctor-specific fields with proper defaults (these are now nullable)
        monthly_quota: 50,
        quota_used: 0,
        quota_reset_at: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString(),
        billing_status: 'trial',
        trial_ends_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        referral_discount_earned: 0,
        total_referrals: 0,
        successful_referrals: 0,
        available_discount_amount: 0
      })
      .eq('id', userId)

    if (error) {
      console.error('Profile creation error:', error)
      return { success: false, message: 'Failed to create user profile. Please try again.' }
    }

    return { success: true, message: 'Profile created successfully.' }
  } catch (error) {
    console.error('Profile creation error:', error)
    return { success: false, message: 'An unexpected error occurred while creating your profile.' }
  }
}

/**
 * Check if user needs phone verification
 * Returns true if user exists but is not approved
 */
export async function needsPhoneVerification(userId: string): Promise<boolean> {
  try {
    const supabase = await createClient()
    
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('approved')
      .eq('id', userId)
      .single()

    if (error || !profile) {
      return false
    }

    return !profile.approved
  } catch (error) {
    console.error('Phone verification check error:', error)
    return false
  }
}

/**
 * Get user data for phone verification
 */
export async function getUserForVerification(userId: string): Promise<{ phone: string; email: string } | null> {
  try {
    const supabase = await createClient()
    
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('phone, email')
      .eq('id', userId)
      .single()

    if (error || !profile) {
      return null
    }

    return {
      phone: profile.phone || '',
      email: profile.email
    }
  } catch (error) {
    console.error('User verification data fetch error:', error)
    return null
  }
}
