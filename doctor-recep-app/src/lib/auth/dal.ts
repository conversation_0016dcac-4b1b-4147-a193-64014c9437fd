// This file is now replaced by /lib/auth/supabase-helpers.ts
// Keeping this file for backward compatibility during migration

// Type alias for backward compatibility
type Doctor = Profile

export {
  verifySession,
  checkSession,
  getUser as getUser,
  getUserOptional
} from './supabase-helpers'

import { cache } from 'react'
import { createClient } from '@/lib/supabase/server'
import { Profile } from '@/lib/types'
import { isDoctorProfile } from '@/lib/guards'

export const getUserById = cache(async (userId: string): Promise<Profile | null> => {
  try {
    const supabase = await createClient()
    const { data: user, error } = await supabase
      .from('profiles')
      .select('id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Failed to fetch user by ID:', error.message || error)
      return null
    }

    if (!user) return null
    // Return user without template_config conversion since it's removed
    return {
      ...user,
    } as unknown as Doctor
  } catch (error) {
    console.error('Failed to fetch user by ID:', error instanceof Error ? error.message : error)
    return null
  }
})

// Get quota information for a doctor (with type safety)
export const getDoctorQuota = cache(async (userId: string) => {
  try {
    const supabase = await createClient()
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Failed to fetch profile:', error.message || error)
      return null
    }

    console.log('Profile data for quota check:', {
      id: profile?.id,
      role: profile?.role,
      monthly_quota: profile?.monthly_quota,
      quota_used: profile?.quota_used
    })

    // Use type guard to ensure safe access to doctor-specific fields
    if (!isDoctorProfile(profile)) {
      console.error('User is not a doctor profile, cannot access quota. Role:', profile?.role)
      return null
    }

    const quotaRemaining = profile.monthly_quota - profile.quota_used
    const quotaPercentage = Math.round((profile.quota_used / profile.monthly_quota) * 100)
    const resetDate = profile.quota_reset_at ? new Date(profile.quota_reset_at) : new Date()
    const daysUntilReset = Math.ceil((resetDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))

    return {
      monthly_quota: profile.monthly_quota,
      quota_used: profile.quota_used,
      quota_remaining: quotaRemaining,
      quota_percentage: quotaPercentage,
      quota_reset_at: profile.quota_reset_at,
      days_until_reset: Math.max(0, daysUntilReset),
    }
  } catch (error) {
    console.error('Failed to fetch quota:', error instanceof Error ? error.message : error)
    return null
  }
})
