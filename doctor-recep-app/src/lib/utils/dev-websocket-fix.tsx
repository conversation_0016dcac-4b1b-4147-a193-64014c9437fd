/**
 * Development WebSocket Error Suppression
 * 
 * This utility helps suppress the common WebSocket connection errors
 * that occur during Next.js development due to Hot Module Replacement (HMR).
 * 
 * The error "WebSocket connection to 'ws://localhost:3004/_next/webpack-hmr' failed"
 * is a development-only issue and doesn't affect production.
 */

/**
 * Suppress WebSocket connection errors in development
 * Call this in your root layout or main component during development
 */
export function suppressDevWebSocketErrors() {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  // Suppress WebSocket connection errors in the console
  const originalError = console.error
  console.error = (...args: any[]) => {
    const message = args[0]
    
    // Filter out common HMR WebSocket errors
    if (
      typeof message === 'string' && 
      (
        message.includes('WebSocket connection to') ||
        message.includes('webpack-hmr') ||
        message.includes('WebSocket is closed due to suspension') ||
        message.includes('Failed to fetch dynamically imported module')
      )
    ) {
      // Optionally log a simplified message
      // console.log('🔄 HMR WebSocket reconnecting...')
      return
    }
    
    // Allow all other errors to be logged normally
    originalError.apply(console, args)
  }

  // Also handle WebSocket errors at the window level
  if (typeof window !== 'undefined') {
    window.addEventListener('error', (event) => {
      if (
        event.message?.includes('WebSocket') ||
        event.message?.includes('webpack-hmr')
      ) {
        event.preventDefault()
        return false
      }
    })

    // Handle unhandled promise rejections related to WebSocket
    window.addEventListener('unhandledrejection', (event) => {
      if (
        event.reason?.message?.includes('WebSocket') ||
        event.reason?.message?.includes('webpack-hmr')
      ) {
        event.preventDefault()
        return false
      }
    })
  }
}

/**
 * Alternative approach: Custom WebSocket wrapper for development
 * This can be used if you need more control over WebSocket behavior
 */
export function createDevWebSocketWrapper() {
  if (process.env.NODE_ENV !== 'development' || typeof window === 'undefined') {
    return
  }

  const OriginalWebSocket = window.WebSocket
  
  window.WebSocket = class extends OriginalWebSocket {
    constructor(url: string | URL, protocols?: string | string[]) {
      super(url, protocols)
      
      // Suppress connection errors for HMR
      this.addEventListener('error', (event) => {
        const urlString = url.toString()
        if (urlString.includes('_next/webpack-hmr')) {
          event.stopPropagation()
          event.preventDefault()
        }
      })
      
      this.addEventListener('close', (event) => {
        const urlString = url.toString()
        if (urlString.includes('_next/webpack-hmr')) {
          // Suppress close events for HMR
          event.stopPropagation()
        }
      })
    }
  }
}

/**
 * Development-only component to handle WebSocket errors
 * Add this to your root layout during development
 */
export function DevWebSocketErrorHandler({ children }: { children: React.ReactNode }) {
  // Always call useEffect, but conditionally execute the logic
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      suppressDevWebSocketErrors()
    }
  }, [])

  return <>{children}</>
}

// Re-export React for the component above
import React from 'react'

/**
 * Instructions for use:
 * 
 * 1. In your root layout (app/layout.tsx), wrap your content:
 * 
 * ```tsx
 * import { DevWebSocketErrorHandler } from '@/lib/utils/dev-websocket-fix'
 * 
 * export default function RootLayout({ children }) {
 *   return (
 *     <html>
 *       <body>
 *         <DevWebSocketErrorHandler>
 *           {children}
 *         </DevWebSocketErrorHandler>
 *       </body>
 *     </html>
 *   )
 * }
 * ```
 * 
 * 2. Or call the function directly in a useEffect:
 * 
 * ```tsx
 * import { suppressDevWebSocketErrors } from '@/lib/utils/dev-websocket-fix'
 * 
 * useEffect(() => {
 *   suppressDevWebSocketErrors()
 * }, [])
 * ```
 * 
 * 3. For Next.js configuration approach, add to next.config.js:
 * 
 * ```js
 * const nextConfig = {
 *   webpack: (config, { dev, isServer }) => {
 *     if (dev && !isServer) {
 *       // Suppress HMR WebSocket errors in development
 *       config.infrastructureLogging = {
 *         level: 'error',
 *         debug: /webpack/
 *       }
 *     }
 *     return config
 *   }
 * }
 * ```
 */
