/**
 * CRITICAL SECURITY IMPROVEMENTS IMPLEMENTED:
 *
 * 1. RLS ENABLED ON webhook_outbox TABLE
 *    - Row Level Security enabled with postgres-only access policy
 *    - Prevents unauthorized access to sensitive webhook payloads
 *
 * 2. FUNCTION SEARCH PATH SECURITY
 *    - generate_referral_code() and update_updated_at_column() secured with fixed search_path
 *    - Prevents function hijacking attacks via malicious schemas
 *
 * 3. OPTIMIZED RLS POLICIES
 *    - Multiple permissive policies consolidated for better performance
 *    - auth.uid() wrapped in subqueries to reduce volatile function calls
 *    - Significant performance improvement for large datasets
 *
 * 4. PERFORMANCE INDEXES ADDED
 *    - Foreign key columns indexed for faster JOINs and cascading operations
 *    - Indexes on: billing_transactions(created_by, plan_id), contact_requests(doctor_id),
 *      profiles(approved_by), referral_discounts(applied_to_transaction_id, doctor_id, referral_analytics_id),
 *      usage_logs(consultation_id)
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      billing_plans: {
        Row: {
          active: boolean
          created_at: string
          description: string | null
          features: Json | null
          id: string
          monthly_price: number
          name: string
          quota_limit: number
          updated_at: string
        }
        Insert: {
          active?: boolean
          created_at?: string
          description?: string | null
          features?: Json | null
          id?: string
          monthly_price: number
          name: string
          quota_limit: number
          updated_at?: string
        }
        Update: {
          active?: boolean
          created_at?: string
          description?: string | null
          features?: Json | null
          id?: string
          monthly_price?: number
          name?: string
          quota_limit?: number
          updated_at?: string
        }
        Relationships: []
      }
      billing_transactions: {
        Row: {
          amount: number
          billing_period_end: string
          billing_period_start: string
          created_at: string
          created_by: string | null
          discount_amount: number
          doctor_id: string
          final_amount: number
          id: string
          metadata: Json | null
          notes: string | null
          payment_date: string | null
          payment_method: string | null
          payment_reference: string | null
          payment_status: string
          plan_id: string | null
          updated_at: string
        }
        Insert: {
          amount: number
          billing_period_end: string
          billing_period_start: string
          created_at?: string
          created_by?: string | null
          discount_amount?: number
          doctor_id: string
          final_amount: number
          id?: string
          metadata?: Json | null
          notes?: string | null
          payment_date?: string | null
          payment_method?: string | null
          payment_reference?: string | null
          payment_status?: string
          plan_id?: string | null
          updated_at?: string
        }
        Update: {
          amount?: number
          billing_period_end?: string
          billing_period_start?: string
          created_at?: string
          created_by?: string | null
          discount_amount?: number
          doctor_id?: string
          final_amount?: number
          id?: string
          metadata?: Json | null
          notes?: string | null
          payment_date?: string | null
          payment_method?: string | null
          payment_reference?: string | null
          payment_status?: string
          plan_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "billing_transactions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_transactions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_transactions_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_transactions_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_transactions_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "billing_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      consultations: {
        Row: {
          additional_audio_urls: Json | null
          additional_notes: string | null
          ai_generated_note: string | null
          consultation_type: Database["public"]["Enums"]["consultation_type"]
          created_at: string
          doctor_id: string | null
          doctor_notes: string | null
          edited_note: string | null
          file_retention_until: string | null
          fts: unknown | null
          id: string
          image_urls: Json | null
          patient_name: string | null
          patient_number: number | null
          primary_audio_url: string
          status: Database["public"]["Enums"]["consultation_status"]
          submitted_by: string
          total_file_size_bytes: number | null
          updated_at: string
        }
        Insert: {
          additional_audio_urls?: Json | null
          additional_notes?: string | null
          ai_generated_note?: string | null
          consultation_type?: Database["public"]["Enums"]["consultation_type"]
          created_at?: string
          doctor_id?: string | null
          doctor_notes?: string | null
          edited_note?: string | null
          file_retention_until?: string | null
          fts?: unknown | null
          id?: string
          image_urls?: Json | null
          patient_name?: string | null
          patient_number?: number | null
          primary_audio_url: string
          status?: Database["public"]["Enums"]["consultation_status"]
          submitted_by: string
          total_file_size_bytes?: number | null
          updated_at?: string
        }
        Update: {
          additional_audio_urls?: Json | null
          additional_notes?: string | null
          ai_generated_note?: string | null
          consultation_type?: Database["public"]["Enums"]["consultation_type"]
          created_at?: string
          doctor_id?: string | null
          doctor_notes?: string | null
          edited_note?: string | null
          file_retention_until?: string | null
          fts?: unknown | null
          id?: string
          image_urls?: Json | null
          patient_name?: string | null
          patient_number?: number | null
          primary_audio_url?: string
          status?: Database["public"]["Enums"]["consultation_status"]
          submitted_by?: string
          total_file_size_bytes?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "consultations_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "consultations_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      contact_requests: {
        Row: {
          clinic_name: string | null
          contacted_at: string | null
          created_at: string
          doctor_email: string
          doctor_id: string | null
          doctor_name: string
          id: string
          message: string | null
          phone_number: string | null
          request_type: string
          resolved_at: string | null
          status: Database["public"]["Enums"]["contact_request_status"]
          updated_at: string
        }
        Insert: {
          clinic_name?: string | null
          contacted_at?: string | null
          created_at?: string
          doctor_email: string
          doctor_id?: string | null
          doctor_name: string
          id?: string
          message?: string | null
          phone_number?: string | null
          request_type: string
          resolved_at?: string | null
          status?: Database["public"]["Enums"]["contact_request_status"]
          updated_at?: string
        }
        Update: {
          clinic_name?: string | null
          contacted_at?: string | null
          created_at?: string
          doctor_email?: string
          doctor_id?: string | null
          doctor_name?: string
          id?: string
          message?: string | null
          phone_number?: string | null
          request_type?: string
          resolved_at?: string | null
          status?: Database["public"]["Enums"]["contact_request_status"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contact_requests_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contact_requests_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          approved: boolean
          approved_at: string | null
          approved_by: string | null
          available_discount_amount: number | null
          billing_status: Database["public"]["Enums"]["billing_status"] | null
          clinic_name: string | null
          conversion_date: string | null
          created_at: string
          current_plan_id: string | null
          email: string
          id: string
          last_payment_date: string | null
          monthly_quota: number | null
          name: string
          next_billing_date: string | null
          phone: string | null
          quota_reset_at: string | null
          quota_used: number | null
          referral_code: string | null
          referral_discount_earned: number | null
          referred_by: string | null
          role: Database["public"]["Enums"]["user_role"]
          successful_referrals: number | null
          total_referrals: number | null
          trial_ends_at: string | null
          updated_at: string
        }
        Insert: {
          approved?: boolean
          approved_at?: string | null
          approved_by?: string | null
          available_discount_amount?: number | null
          billing_status?: Database["public"]["Enums"]["billing_status"] | null
          clinic_name?: string | null
          conversion_date?: string | null
          created_at?: string
          current_plan_id?: string | null
          email: string
          id: string
          last_payment_date?: string | null
          monthly_quota?: number | null
          name: string
          next_billing_date?: string | null
          phone?: string | null
          quota_reset_at?: string | null
          quota_used?: number | null
          referral_code?: string | null
          referral_discount_earned?: number | null
          referred_by?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          successful_referrals?: number | null
          total_referrals?: number | null
          trial_ends_at?: string | null
          updated_at?: string
        }
        Update: {
          approved?: boolean
          approved_at?: string | null
          approved_by?: string | null
          available_discount_amount?: number | null
          billing_status?: Database["public"]["Enums"]["billing_status"] | null
          clinic_name?: string | null
          conversion_date?: string | null
          created_at?: string
          current_plan_id?: string | null
          email?: string
          id?: string
          last_payment_date?: string | null
          monthly_quota?: number | null
          name?: string
          next_billing_date?: string | null
          phone?: string | null
          quota_reset_at?: string | null
          quota_used?: number | null
          referral_code?: string | null
          referral_discount_earned?: number | null
          referred_by?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          successful_referrals?: number | null
          total_referrals?: number | null
          trial_ends_at?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_current_plan_id_fkey"
            columns: ["current_plan_id"]
            isOneToOne: false
            referencedRelation: "billing_plans"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_referred_by_fkey"
            columns: ["referred_by"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_referred_by_fkey"
            columns: ["referred_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      referral_analytics: {
        Row: {
          conversion_date: string | null
          created_at: string
          discount_earned: number
          id: string
          metadata: Json | null
          referral_code: string
          referred_doctor_id: string | null
          referrer_id: string | null
          signup_date: string
          status: string
          updated_at: string
        }
        Insert: {
          conversion_date?: string | null
          created_at?: string
          discount_earned?: number
          id?: string
          metadata?: Json | null
          referral_code: string
          referred_doctor_id?: string | null
          referrer_id?: string | null
          signup_date?: string
          status?: string
          updated_at?: string
        }
        Update: {
          conversion_date?: string | null
          created_at?: string
          discount_earned?: number
          id?: string
          metadata?: Json | null
          referral_code?: string
          referred_doctor_id?: string | null
          referrer_id?: string | null
          signup_date?: string
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "referral_analytics_referred_doctor_id_fkey"
            columns: ["referred_doctor_id"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_analytics_referred_doctor_id_fkey"
            columns: ["referred_doctor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_analytics_referrer_id_fkey"
            columns: ["referrer_id"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_analytics_referrer_id_fkey"
            columns: ["referrer_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      referral_discounts: {
        Row: {
          applied_at: string | null
          applied_to_transaction_id: string | null
          created_at: string
          discount_amount: number
          doctor_id: string
          id: string
          original_amount: number
          referral_analytics_id: string
          status: string
          updated_at: string
          valid_until: string
        }
        Insert: {
          applied_at?: string | null
          applied_to_transaction_id?: string | null
          created_at?: string
          discount_amount: number
          doctor_id: string
          id?: string
          original_amount: number
          referral_analytics_id: string
          status?: string
          updated_at?: string
          valid_until: string
        }
        Update: {
          applied_at?: string | null
          applied_to_transaction_id?: string | null
          created_at?: string
          discount_amount?: number
          doctor_id?: string
          id?: string
          original_amount?: number
          referral_analytics_id?: string
          status?: string
          updated_at?: string
          valid_until?: string
        }
        Relationships: [
          {
            foreignKeyName: "referral_discounts_applied_to_transaction_id_fkey"
            columns: ["applied_to_transaction_id"]
            isOneToOne: false
            referencedRelation: "billing_transactions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_discounts_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_discounts_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_discounts_referral_analytics_id_fkey"
            columns: ["referral_analytics_id"]
            isOneToOne: false
            referencedRelation: "referral_analytics"
            referencedColumns: ["id"]
          },
        ]
      }
      usage_logs: {
        Row: {
          action_type: string
          consultation_id: string | null
          created_at: string
          doctor_id: string | null
          id: string
          metadata: Json | null
          quota_after: number | null
          quota_before: number | null
        }
        Insert: {
          action_type: string
          consultation_id?: string | null
          created_at?: string
          doctor_id?: string | null
          id?: string
          metadata?: Json | null
          quota_after?: number | null
          quota_before?: number | null
        }
        Update: {
          action_type?: string
          consultation_id?: string | null
          created_at?: string
          doctor_id?: string | null
          id?: string
          metadata?: Json | null
          quota_after?: number | null
          quota_before?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "usage_logs_consultation_id_fkey"
            columns: ["consultation_id"]
            isOneToOne: false
            referencedRelation: "consultations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "admin_doctors_with_stats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      // SECURITY: RLS enabled with postgres-only access policy for webhook queue processing
      webhook_outbox: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          event_type: string
          payload: Json
          webhook_url: string
          headers: Json
          status: Database["public"]["Enums"]["webhook_status"]
          attempts: number
          max_attempts: number
          next_attempt_at: string
          last_response_status: number | null
          last_response_body: string | null
          last_error: string | null
          delivered_at: string | null
          source_table: string | null
          source_id: string | null
          timeout_milliseconds: number
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          event_type: string
          payload: Json
          webhook_url: string
          headers?: Json
          status?: Database["public"]["Enums"]["webhook_status"]
          attempts?: number
          max_attempts?: number
          next_attempt_at?: string
          last_response_status?: number | null
          last_response_body?: string | null
          last_error?: string | null
          delivered_at?: string | null
          source_table?: string | null
          source_id?: string | null
          timeout_milliseconds?: number
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          event_type?: string
          payload?: Json
          webhook_url?: string
          headers?: Json
          status?: Database["public"]["Enums"]["webhook_status"]
          attempts?: number
          max_attempts?: number
          next_attempt_at?: string
          last_response_status?: number | null
          last_response_body?: string | null
          last_error?: string | null
          delivered_at?: string | null
          source_table?: string | null
          source_id?: string | null
          timeout_milliseconds?: number
        }
        Relationships: []
      }
    }
    Views: {
      admin_dashboard_summary: {
        Row: {
          approved_doctors: number | null
          pending_approvals: number | null
          quota_usage_percentage: number | null
          total_ai_generations: number | null
          total_consultations: number | null
          total_doctors: number | null
        }
        Relationships: []
      }
      admin_doctors_with_stats: {
        Row: {
          approved: boolean | null
          approved_at: string | null
          approved_by: string | null
          available_discount_amount: number | null
          billing_status: Database["public"]["Enums"]["billing_status"] | null
          clinic_name: string | null
          created_at: string | null
          current_plan_id: string | null
          email: string | null
          id: string | null
          last_activity: string | null
          last_payment_date: string | null
          monthly_quota: number | null
          name: string | null
          next_billing_date: string | null
          phone: string | null
          quota_percentage: number
          quota_reset_at: string | null
          quota_used: number | null
          referral_code: string | null
          referral_discount_earned: number | null
          referred_by: string | null
          referred_by_name: string | null
          successful_referrals: number | null
          this_month_generations: number | null
          total_consultations: number | null
          trial_ends_at: string | null
          updated_at: string | null
        }
        Insert: {
          approved?: boolean | null
          approved_at?: string | null
          approved_by?: never
          billing_status?: Database["public"]["Enums"]["billing_status"] | null
          clinic_name?: string | null
          created_at?: string | null
          email?: string | null
          id?: string | null
          last_activity?: never
          monthly_quota?: number | null
          name?: string | null
          phone?: string | null
          quota_percentage?: never
          quota_reset_at?: string | null
          quota_used?: number | null
          referral_code?: string | null
          referred_by?: never
          this_month_generations?: never
          total_consultations?: never
          trial_ends_at?: string | null
          updated_at?: string | null
        }
        Update: {
          approved?: boolean | null
          approved_at?: string | null
          approved_by?: never
          billing_status?: Database["public"]["Enums"]["billing_status"] | null
          clinic_name?: string | null
          created_at?: string | null
          email?: string | null
          id?: string | null
          last_activity?: never
          monthly_quota?: number | null
          name?: string | null
          phone?: string | null
          quota_percentage?: never
          quota_reset_at?: string | null
          quota_used?: number | null
          referral_code?: string | null
          referred_by?: never
          this_month_generations?: never
          total_consultations?: never
          trial_ends_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      doctor_referral_summary: {
        Row: {
          doctor_id: string | null
          referral_code: string | null
          total_referrals: number | null
          successful_referrals: number | null
          discount_earned: number | null
          referred_by: string | null
          referrer_name: string | null
          referrer_code: string | null
          pending_referrals: number | null
          recent_referrals: Json | null
        }
        Insert: {
          doctor_id?: never
          referral_code?: never
          total_referrals?: never
          successful_referrals?: never
          discount_earned?: never
          referred_by?: never
          referrer_name?: never
          referrer_code?: never
          pending_referrals?: never
          recent_referrals?: never
        }
        Update: {
          doctor_id?: never
          referral_code?: never
          total_referrals?: never
          successful_referrals?: never
          discount_earned?: never
          referred_by?: never
          referrer_name?: never
          referrer_code?: never
          pending_referrals?: never
          recent_referrals?: never
        }
        Relationships: []
      }
      doctor_consultation_stats: {
        Row: {
          doctor_id: string | null
          total_consultations: number | null
          pending_consultations: number | null
          generated_consultations: number | null
          approved_consultations: number | null
          archived_consultations: number | null
          today_consultations: number | null
          this_week_consultations: number | null
          this_month_consultations: number | null
          last_consultation_date: string | null
          first_consultation_date: string | null
        }
        Insert: {
          doctor_id?: never
          total_consultations?: never
          pending_consultations?: never
          generated_consultations?: never
          approved_consultations?: never
          archived_consultations?: never
          today_consultations?: never
          this_week_consultations?: never
          this_month_consultations?: never
          last_consultation_date?: never
          first_consultation_date?: never
        }
        Update: {
          doctor_id?: never
          total_consultations?: never
          pending_consultations?: never
          generated_consultations?: never
          approved_consultations?: never
          archived_consultations?: never
          today_consultations?: never
          this_week_consultations?: never
          this_month_consultations?: never
          last_consultation_date?: never
          first_consultation_date?: never
        }
        Relationships: []
      }
    }
    Functions: {
      // SECURITY: Fixed search_path to prevent function hijacking
      generate_referral_code: {
        Args: { name: string }
        Returns: string
      }
      // SECURITY: Fixed search_path, used in optimized RLS policies
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
    }
    Enums: {
      billing_status: "trial" | "active" | "suspended" | "cancelled"
      consultation_status:
        | "pending_generation"
        | "generated"
        | "approved"
        | "archived"
      consultation_type:
        | "outpatient"
        | "discharge"
        | "surgery"
        | "radiology"
        | "dermatology"
        | "cardiology_echo"
        | "ivf_cycle"
        | "pathology"
      contact_request_status: "pending" | "contacted" | "resolved"
      user_role: "doctor" | "admin" | "super_admin"
      webhook_status: "pending" | "processing" | "delivered" | "failed" | "dead_letter"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      billing_status: ["trial", "active", "suspended", "cancelled"],
      consultation_status: [
        "pending_generation",
        "generated",
        "approved",
        "archived",
      ],
      consultation_type: [
        "outpatient",
        "discharge",
        "surgery",
        "radiology",
        "dermatology",
        "cardiology_echo",
        "ivf_cycle",
        "pathology",
      ],
      contact_request_status: ["pending", "contacted", "resolved"],
      user_role: ["doctor", "admin", "super_admin"],
      webhook_status: ["pending", "processing", "delivered", "failed", "dead_letter"],
    },
  },
} as const

// Direct type exports for easier use in application code
export type UserRole = Database["public"]["Enums"]["user_role"]
export type ConsultationStatus = Database["public"]["Enums"]["consultation_status"]
export type ConsultationType = Database["public"]["Enums"]["consultation_type"]
export type ContactRequestStatus = Database["public"]["Enums"]["contact_request_status"]
export type BillingStatus = Database["public"]["Enums"]["billing_status"]
export type WebhookStatus = Database["public"]["Enums"]["webhook_status"]

// Legacy types for backward compatibility
export type ContactRequestType = string // request_type is just string in the schema
export type PaymentStatus = string // payment_status is just string in the schema
export type ActionType = string // action_type is just string in the schema

// Consultation type labels for UI display
export const CONSULTATION_TYPE_LABELS: Record<ConsultationType, string> = {
  outpatient: 'Outpatient Consultation',
  discharge: 'Discharge Summary',
  surgery: 'Operative Note',
  radiology: 'Radiology Report',
  dermatology: 'Dermatology Note',
  cardiology_echo: 'Echocardiogram Report',
  ivf_cycle: 'IVF Cycle Summary',
  pathology: 'Histopathology Report'
}

// Table type exports for easier use
export type Profile = Tables<'profiles'>
export type Consultation = Tables<'consultations'>
export type ContactRequest = Tables<'contact_requests'>
export type BillingPlan = Tables<'billing_plans'>
export type BillingTransaction = Tables<'billing_transactions'>
export type ReferralAnalytics = Tables<'referral_analytics'>
export type ReferralDiscount = Tables<'referral_discounts'>
export type UsageLog = Tables<'usage_logs'>
export type WebhookOutbox = Tables<'webhook_outbox'>

// View type exports
export type AdminDashboardSummary = Tables<'admin_dashboard_summary'>
export type AdminDoctorWithStats = Tables<'admin_doctors_with_stats'>
export type DoctorReferralSummary = Tables<'doctor_referral_summary'>
export type DoctorConsultationStats = Tables<'doctor_consultation_stats'>

// Role-based profile types
export type DoctorProfile = Profile & {
  // Doctor-specific fields that are required
  monthly_quota: number
  quota_used: number
  quota_reset_at: string
  billing_status: BillingStatus
  available_discount_amount: number
  referral_discount_earned: number
  total_referrals: number
  successful_referrals: number
  role: 'doctor'
}

export type AdminProfile = Profile & {
  // Admin-specific fields (doctor fields are null)
  monthly_quota: null
  quota_used: null
  quota_reset_at: null
  billing_status: null
  available_discount_amount: null
  referral_discount_earned: null
  total_referrals: null
  successful_referrals: null
  clinic_name: null
  phone: null
  referral_code: null
  referred_by: null
  trial_ends_at: null
  next_billing_date: null
  last_payment_date: null
  current_plan_id: null
  conversion_date: null
  role: 'admin' | 'super_admin'
  approved: true // Admins are always approved
}

// Legacy type aliases for backward compatibility during migration
export type Doctor = DoctorProfile
export type Admin = AdminProfile

// Utility type for creating new profiles based on role
export type CreateDoctorProfile = Omit<DoctorProfile, 'id' | 'created_at' | 'updated_at' | 'approved_at' | 'approved_by'> & {
  id?: string
  created_at?: string
  updated_at?: string
  approved_at?: string | null
  approved_by?: string | null
}

export type CreateAdminProfile = Omit<AdminProfile, 'id' | 'created_at' | 'updated_at' | 'approved_at' | 'approved_by'> & {
  id?: string
  created_at?: string
  updated_at?: string
  approved_at?: string | null
  approved_by?: string | null
}

// API Response type
export type ApiResponse<T> = { success: true; data: T } | { success: false; error: string }

// Form state type
export interface FormState {
  success: boolean
  message: string
  fieldErrors?: { [key: string]: string[] }
  data?: any
}

// Dashboard stats interfaces
export interface AdminDashboardStats {
  total_doctors: number
  pending_approvals: number
  approved_doctors: number
  total_consultations: number
  total_ai_generations: number
  quota_usage_percentage: number
}

export interface DashboardStats {
  total_consultations: number
  pending_consultations: number
  generated_consultations: number
  approved_consultations: number
  today_consultations: number
}

export interface QuotaInfo {
  monthly_quota: number
  quota_used: number
  quota_remaining: number
  quota_percentage: number
  quota_reset_at: string | null
  days_until_reset: number
}

export interface ReferralInfo {
  referral_code: string
  total_referrals: number
  successful_referrals: number
  pending_referrals: number
  discount_earned: number
  referred_by?: {
    name: string
    referral_code: string
  } | null
  recent_referrals: Array<{
    id: string
    name: string
    email: string
    signup_date: string
    conversion_date: string | null
    status: 'pending' | 'converted' | 'expired'
  }>
}

export type DoctorWithStats = Profile & {
  total_consultations: number
  this_month_generations: number
  quota_percentage: number
  last_activity?: string
}

export type AdminActionRequest =
  | { action: 'approve'; doctor_id: string }
  | { action: 'reject'; doctor_id: string }
  | { action: 'update_quota'; doctor_id: string; data: { quota: number; reason?: string } }
  | { action: 'disable'; doctor_id: string }
  | { action: 'enable'; doctor_id: string }

export interface ImageFile {
  id: string
  file: File
  preview?: string
  name: string
  type: string
  size: number
}

export interface ImageCaptureState {
  images: ImageFile[]
  status?: 'idle' | 'capturing' | 'uploaded' | 'error'
  error: string | null
}

export interface AudioRecordingState {
  isRecording: boolean
  duration: number
  audioBlob?: Blob
  audioFile?: File
  error?: string | null
  status?: 'idle' | 'recording' | 'recorded' | 'uploading' | 'uploaded' | 'error'
}

export interface SessionPayload {
  userId: string
  expiresAt: Date
  adminId?: string
  role?: 'admin' | 'super_admin'
}
