# Backend and database files
python-backend/
database/

# CMS and content management
../celer-ai-blog/
../example/


# Development and testing
tests/
scripts/
*.log
*.tsbuildinfo

# Database files (SQL dumps, schemas, backups)
*.sql
*.dmp
*.dump

# Development files and examples
*.py
*.txt
exampl.py
example_mainpy.txt
project_tree.txt
working.txt

# Configuration examples (not needed for deployment)
r2-cors-*.json

# Documentation (optional - remove if you want docs deployed)
*.md
!README.md

# Development scripts
start-*.sh
deploy-*.sh

# IDE and OS files
.DS_Store
.vscode/
.idea/
*.swp
*.swo

# Environment files
.env*
!.env.example
