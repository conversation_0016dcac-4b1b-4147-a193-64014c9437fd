/** @type {import('next').NextConfig} */
const nextConfig = {
  // Server Actions configuration for large audio files
  experimental: {
    serverActions: {
      bodySizeLimit: '10mb', // Allow up to 10MB for audio files
    },
    optimizePackageImports: ['lucide-react', '@sanity/client', '@portabletext/react'],
  },

  // Turbopack configuration (moved from experimental.turbo)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  
  // Image optimization for Cloudflare R2 storage and Sanity CDN
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'celerai.tallyup.pro',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'celerai.live',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'www.celerai.live',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        port: '',
        pathname: '/images/**',
      },
    ],
  },
  
  // Security headers and performance optimization
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // DNS prefetch for performance
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          // Content Security Policy for healthcare compliance
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://ssl.google-analytics.com https://vercel.live https://va.vercel-scripts.com https://accounts.google.com https://apis.google.com",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
              "font-src 'self' https://fonts.gstatic.com",
              "img-src 'self' data: blob: https://cdn.sanity.io https://celerai.tallyup.pro https://celerai.live https://www.celerai.live https://www.google-analytics.com https://ssl.google-analytics.com",
              "media-src 'self' blob: https://celerai.tallyup.pro https://celerai.live https://www.celerai.live",
              // Enhanced connect-src to include localhost development and all necessary domains
              [
                "connect-src 'self'",
                // Development localhost support
                ...(process.env.NODE_ENV === 'development' ? [
                  'http://localhost:3004',
                  'https://localhost:3004',
                  'ws://localhost:3004',
                  'wss://localhost:3004',
                  'http://localhost:3000', // Backup port
                  'ws://localhost:3000',
                  'wss://localhost:3000'
                ] : []),
                // Supabase connections (HTTPS and WebSocket) - CORRECTED URL
                'https://edojplwmwtytpdssrxbh.supabase.co',
                'wss://edojplwmwtytpdssrxbh.supabase.co',
                // Supabase Auth endpoints - ENTERPRISE FIX for password reset
                'https://edojplwmwtytpdssrxbh.supabase.co/auth/v1/user',
                'https://edojplwmwtytpdssrxbh.supabase.co/auth/v1/recover',
                'https://edojplwmwtytpdssrxbh.supabase.co/auth/v1/token',
                // Production domains
                'https://celerai.live',
                'https://www.celerai.live',
                'wss://celerai.live',
                'wss://www.celerai.live',
                'https://celerai.tallyup.pro',
                'wss://celerai.tallyup.pro',
                // CMS and Analytics
                'https://api.sanity.io',
                'https://cdn.sanity.io',
                'https://www.google-analytics.com',
                'https://analytics.google.com',
                'https://ssl.google-analytics.com',
                'https://stats.g.doubleclick.net',
                // Vercel and monitoring
                'wss://vercel.live',
                'https://va.vercel-scripts.com',
                'https://vitals.vercel-insights.com',
                'https://o4509552948609024.ingest.de.sentry.io',
                // SMS service
                'https://cpaas.messagecentral.com',
                // Google Identity Services for One Tap
                'https://accounts.google.com',
                'https://apis.google.com'
              ].join(' '),
              "worker-src 'self' blob:",
              "manifest-src 'self'",
              "frame-src 'self' https://vercel.live https://www.loom.com https://accounts.google.com",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "child-src 'self' blob:",
              // Only upgrade insecure requests in production
              ...(process.env.NODE_ENV === 'production' ? ["upgrade-insecure-requests"] : [])
            ].join('; ')
          },
          // Prevent clickjacking
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          // Prevent MIME type sniffing
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          // Referrer policy for privacy
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
        ],
      },
    ]
  },

  // Redirect configuration for SEO
  async redirects() {
    return [
      {
        source: '/articles/:path*',
        destination: '/blog/:path*',
        permanent: true,
      },
      {
        source: '/tutorials/:path*',
        destination: '/guide/:path*',
        permanent: true,
      },
    ]
  },
}

module.exports = nextConfig


// Injected content via Sentry wizard below

// Conditionally apply Sentry config only when not using Turbopack
const { withSentryConfig } = require("@sentry/nextjs");

// Check if we're using Turbopack
const isTurbopack = process.argv.includes('--turbopack');

if (isTurbopack) {
  // Export config without Sentry when using Turbopack
  module.exports = nextConfig;
} else {
  // Apply Sentry config when using Webpack
  module.exports = withSentryConfig(
    nextConfig,
    {
      // For all available options, see:
      // https://www.npmjs.com/package/@sentry/webpack-plugin#options

      org: "celer-ai",
      project: "javascript-nextjs",

      // Only print logs for uploading source maps in CI
      silent: !process.env.CI,

      // For all available options, see:
      // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

      // Upload a larger set of source maps for prettier stack traces (increases build time)
      widenClientFileUpload: true,

      // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
      // This can increase your server load as well as your hosting bill.
      // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
      // side errors will fail.
      // Disabled when using Turbopack as it's not supported
      ...(isTurbopack ? {} : {  }),

      // Automatically tree-shake Sentry logger statements to reduce bundle size
      disableLogger: true,

      // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
      // See the following for more information:
      // https://docs.sentry.io/product/crons/
      // https://vercel.com/docs/cron-jobs
      automaticVercelMonitors: true,
    }
  );
}
