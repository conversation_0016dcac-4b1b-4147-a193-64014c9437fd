#!/usr/bin/env node

/**
 * Test Doctor Cleanup Script
 * Usage: node database/create/cleanup-test-doctors.js
 * 
 * This script removes test doctor accounts from both auth.users and profiles tables
 * Use with caution - this will permanently delete user data
 */

const { createClient } = require('@supabase/supabase-js')
const readline = require('readline')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  console.error('   Make sure your .env.local file is properly configured.')
  process.exit(1)
}

// Create Supabase client with service role key (bypasses RLS)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve)
  })
}

async function listTestDoctors() {
  console.log('🔍 Searching for test doctor accounts...\n')
  
  try {
    // Find profiles that look like test accounts
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('id, name, email, clinic_name, phone, created_at, approved')
      .eq('role', 'doctor')
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('❌ Error fetching profiles:', error.message)
      return []
    }
    
    if (!profiles || profiles.length === 0) {
      console.log('✅ No doctor profiles found.')
      return []
    }
    
    // Filter for likely test accounts (you can modify these criteria)
    const testDoctors = profiles.filter(profile => {
      const email = profile.email.toLowerCase()
      const name = profile.name.toLowerCase()
      const clinicName = profile.clinic_name?.toLowerCase() || ''
      
      return (
        email.includes('test') ||
        email.includes('demo') ||
        email.includes('example') ||
        name.includes('test') ||
        name.includes('demo') ||
        clinicName.includes('test') ||
        clinicName.includes('demo') ||
        email.includes('+test') ||
        email.includes('temp')
      )
    })
    
    console.log(`📊 Found ${profiles.length} total doctor profiles`)
    console.log(`🧪 Found ${testDoctors.length} potential test accounts:\n`)
    
    if (testDoctors.length > 0) {
      testDoctors.forEach((doctor, index) => {
        console.log(`${index + 1}. ${doctor.name} (${doctor.email})`)
        console.log(`   Clinic: ${doctor.clinic_name || 'N/A'}`)
        console.log(`   Phone: ${doctor.phone || 'N/A'}`)
        console.log(`   Created: ${new Date(doctor.created_at).toLocaleDateString()}`)
        console.log(`   Approved: ${doctor.approved ? '✅' : '❌'}`)
        console.log('')
      })
    }
    
    return testDoctors
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    return []
  }
}

async function deleteTestDoctor(doctorId, doctorEmail) {
  try {
    console.log(`🗑️  Deleting doctor: ${doctorEmail}`)
    
    // Delete from auth.users (this will cascade to profiles due to foreign key)
    const { error: authError } = await supabase.auth.admin.deleteUser(doctorId)
    
    if (authError) {
      console.error(`❌ Error deleting auth user ${doctorEmail}:`, authError.message)
      return false
    }
    
    console.log(`✅ Successfully deleted: ${doctorEmail}`)
    return true
  } catch (error) {
    console.error(`❌ Unexpected error deleting ${doctorEmail}:`, error.message)
    return false
  }
}

async function main() {
  console.log('🧹 Test Doctor Cleanup Tool')
  console.log('============================\n')
  
  const testDoctors = await listTestDoctors()
  
  if (testDoctors.length === 0) {
    console.log('✅ No test doctor accounts found to clean up.')
    rl.close()
    return
  }
  
  console.log('⚠️  WARNING: This will permanently delete the selected accounts!')
  console.log('This action cannot be undone.\n')
  
  const confirmAll = await askQuestion('Do you want to delete ALL test accounts? (y/N): ')
  
  if (confirmAll.toLowerCase() === 'y' || confirmAll.toLowerCase() === 'yes') {
    console.log('\n🗑️  Deleting all test accounts...\n')
    
    let successCount = 0
    for (const doctor of testDoctors) {
      const success = await deleteTestDoctor(doctor.id, doctor.email)
      if (success) successCount++
    }
    
    console.log(`\n✅ Cleanup complete! Deleted ${successCount}/${testDoctors.length} accounts.`)
  } else {
    console.log('\n🔍 Individual account selection:')
    
    for (const doctor of testDoctors) {
      const confirm = await askQuestion(`Delete ${doctor.name} (${doctor.email})? (y/N): `)
      
      if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
        await deleteTestDoctor(doctor.id, doctor.email)
      } else {
        console.log(`⏭️  Skipped: ${doctor.email}`)
      }
    }
    
    console.log('\n✅ Cleanup process completed.')
  }
  
  rl.close()
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Cleanup cancelled by user.')
  rl.close()
  process.exit(0)
})

// Run the script
main().catch((error) => {
  console.error('❌ Script failed:', error.message)
  rl.close()
  process.exit(1)
})
