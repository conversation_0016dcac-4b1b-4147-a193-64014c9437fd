#!/usr/bin/env node

/**
 * Simple Admin User Creation Script
 * Usage: node database/create/create-admin-simple.js
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  console.error('   Make sure your .env.local file is properly configured.')
  process.exit(1)
}

// Create Supabase client with service role key (bypasses RLS)
const supabase = createClient(supabaseUrl, supabaseService<PERSON><PERSON>, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createAdminUser() {
  try {
    console.log('🔧 Creating Admin User in Supabase\n')
    
    // Hardcoded values for simplicity - you can change these
    const name = 'lol'
    const email = 'lol'
    const password = 'lol'  // Change this to your desired password
    const role = 'admin'
    
    console.log(`Creating admin user:`)
    console.log(`Name: ${name}`)
    console.log(`Email: ${email}`)
    console.log(`Role: ${role}`)
    console.log('')
    
    console.log('🚀 Creating user...')
    
    // Step 1: Create user in Supabase Auth with role in metadata
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email for admins
      user_metadata: {
        name,
        role // CRITICAL: Pass the role here for the trigger to read
      }
    })
    
    if (authError) {
      console.error('❌ Failed to create auth user:', authError.message)
      process.exit(1)
    }
    
    console.log('✅ Auth user created successfully')
    console.log('✅ Profile created by trigger with role:', role)
    
    // Step 2: Update the profile to NULL out doctor-specific fields for admins
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        approved: null, // NULL = admin-only access, true = doctor access, false = pending
        approved_at: new Date().toISOString(),
        // NULL out all doctor-specific fields for admin users
        monthly_quota: null,
        quota_used: null,
        quota_reset_at: null,
        billing_status: null,
        available_discount_amount: null,
        referral_discount_earned: null,
        total_referrals: null,
        successful_referrals: null,
        clinic_name: null,
        phone: null,
        referral_code: null,
        referred_by: null,
        trial_ends_at: null,
        next_billing_date: null,
        last_payment_date: null,
        current_plan_id: null,
        conversion_date: null
      })
      .eq('id', authData.user.id)
    
    if (updateError) {
      console.error('❌ Failed to finalize admin profile:', updateError.message)
      
      // Cleanup: Delete the auth user if profile update failed
      await supabase.auth.admin.deleteUser(authData.user.id)
      console.log('🧹 Cleaned up auth user due to profile update failure')
      process.exit(1)
    }
    
    console.log('✅ Admin profile finalized successfully')
    
    // Step 3: Verify the user was created correctly
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()
    
    if (fetchError || !profile) {
      console.error('❌ Failed to verify user creation:', fetchError?.message)
      process.exit(1)
    }
    
    console.log('\n🎉 Admin user created successfully!')
    console.log('📋 User Details:')
    console.log(`   ID: ${profile.id}`)
    console.log(`   Name: ${profile.name}`)
    console.log(`   Email: ${profile.email}`)
    console.log(`   Role: ${profile.role}`)
    console.log(`   Approved: ${profile.approved}`)
    console.log(`   Architecture: Database as Muscle, Application as Brain ✅`)
    console.log(`   Doctor Fields: NULL (as expected for admin) ✅`)
    console.log(`   Created: ${profile.created_at}`)
    
    console.log('\n✨ The admin user can now log in to the application!')
    console.log('🏗️  Architecture Notes:')
    console.log('   • Doctor-specific fields are NULL in database')
    console.log('   • Business logic enforced by TypeScript guards')
    console.log('   • Type guards available in src/lib/guards.ts')
    
    console.log('\n🔗 Next Steps:')
    console.log('   1. Start your dev server: npm run dev')
    console.log('   2. Go to: http://localhost:3004/admin/login')
    console.log('   3. Login with the credentials above')
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    process.exit(1)
  }
}

// Run the script
createAdminUser()
