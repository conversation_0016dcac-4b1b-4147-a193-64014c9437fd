#!/usr/bin/env node

/**
 * Reset Admin Password Script
 * Usage: node database/create/reset-admin-password.js
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  process.exit(1)
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function resetAdminPassword() {
  try {
    console.log('🔧 Resetting Admin Password\n')
    
    const email = 'lol'
    const newPassword = 'lol!'  // New secure password
    
    console.log(`Resetting password for: ${email}`)
    console.log(`New password: ${newPassword}`)
    console.log('')
    
    // Update the user's password
    const { data, error } = await supabase.auth.admin.updateUserById(
      'nope', // Your admin user ID
      { password: newPassword }
    )
    
    if (error) {
      console.error('❌ Failed to reset password:', error.message)
      process.exit(1)
    }
    
    console.log('✅ Password reset successfully!')
    console.log('\n🔗 Login Details:')
    console.log(`   Email: ${email}`)
    console.log(`   Password: ${newPassword}`)
    console.log('\n🚀 Next Steps:')
    console.log('   1. Go to: http://localhost:3004/admin/login')
    console.log('   2. Use the credentials above')
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    process.exit(1)
  }
}

// Run the script
resetAdminPassword()
