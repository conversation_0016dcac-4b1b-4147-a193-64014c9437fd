-- CORRECTED VIEW DEFINITIONS
-- This ensures the views respect the RLS policies of the calling user.

CREATE OR REPLACE VIEW public.admin_dashboard_summary
WITH (security_invoker = true) AS
SELECT
    (SELECT COUNT(*) FROM public.profiles WHERE role = 'doctor') AS total_doctors,
    (SELECT COUNT(*) FROM public.profiles WHERE role = 'doctor' AND approved = false) AS pending_approvals,
    (SELECT COUNT(*) FROM public.profiles WHERE role = 'doctor' AND approved = true) AS approved_doctors,
    (SELECT COUNT(*) FROM public.consultations) AS total_consultations,
    (SELECT COUNT(*) FROM public.usage_logs WHERE action_type = 'ai_generation') AS total_ai_generations,
    (SELECT ROUND((SUM(quota_used)::numeric / NULLIF(SUM(monthly_quota), 0)) * 100, 2) FROM public.profiles WHERE role = 'doctor' AND monthly_quota > 0) AS quota_usage_percentage;

COMMENT ON VIEW public.admin_dashboard_summary IS 'Security invoker view for admin dashboard stats. Runs with the permissions of the querying user.';


CREATE OR REPLACE VIEW public.admin_doctors_with_stats
WITH (security_invoker = true) AS
SELECT
    p.id, p.email, p.name, p.phone, p.clinic_name, p.monthly_quota, p.quota_used, p.quota_reset_at,
    p.approved, p.approved_at, p.referral_code, p.billing_status, p.trial_ends_at, p.created_at, p.updated_at,
    (SELECT name FROM public.profiles ap WHERE ap.id = p.approved_by) AS approved_by,
    (SELECT name FROM public.profiles rp WHERE rp.id = p.referred_by) AS referred_by,
    (SELECT COUNT(*) FROM public.consultations c WHERE c.doctor_id = p.id) AS total_consultations,
    (SELECT COUNT(*) FROM public.usage_logs ul WHERE ul.doctor_id = p.id AND ul.action_type = 'ai_generation' AND ul.created_at >= date_trunc('month', now())) AS this_month_generations,
    (SELECT MAX(ul.created_at) FROM public.usage_logs ul WHERE ul.doctor_id = p.id) AS last_activity,
    COALESCE(ROUND((p.quota_used::numeric / NULLIF(p.monthly_quota, 0)) * 100, 2), 0) AS quota_percentage
FROM public.profiles p
WHERE p.role = 'doctor';

COMMENT ON VIEW public.admin_doctors_with_stats IS 'Security invoker view for detailed doctor stats. Runs with the permissions of the querying user.';