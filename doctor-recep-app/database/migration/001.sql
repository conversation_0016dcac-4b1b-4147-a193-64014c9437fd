-- Celer AI - Enterprise Migration Script v2.0 (Corrected & Hardened)
-- Written by: AI-CTO
-- Date: 2025-06-30
-- Description: This script sets up the new, optimized database schema,
--              integrating with Supabase Auth and implementing robust
--              security and performance patterns. It is now idempotent
--              and safe to run on a new Supabase project.

-- Turn off notices for cleaner execution log
SET client_min_messages TO warning;

-- ============================================================================
-- 1. SCHEMA & EXTENSION SETUP (Hardened)
-- ============================================================================
-- Create a dedicated schema for extensions to keep the public schema clean.
CREATE SCHEMA IF NOT EXISTS extensions;

-- Enable required extensions, creating them within the 'extensions' schema.
-- This is the correct, idempotent way to handle extensions.
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;
CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions; -- Preferred for gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS pg_net WITH SCHEMA extensions;
CREATE EXTENSION IF NOT EXISTS http WITH SCHEMA extensions;


-- ============================================================================
-- 2. CUSTOM DATA TYPES (ENUMs for Data Integrity & Performance)
-- ============================================================================
-- Drop types if they exist to ensure a clean slate, then create them.
DROP TYPE IF EXISTS public.user_role CASCADE;
DROP TYPE IF EXISTS public.consultation_status CASCADE;
DROP TYPE IF EXISTS public.billing_status CASCADE;
DROP TYPE IF EXISTS public.contact_request_status CASCADE;
DROP TYPE IF EXISTS public.consultation_type CASCADE;

CREATE TYPE public.user_role AS ENUM ('doctor', 'admin', 'super_admin');
CREATE TYPE public.consultation_status AS ENUM ('pending_generation', 'generated', 'approved', 'archived');
CREATE TYPE public.billing_status AS ENUM ('trial', 'active', 'suspended', 'cancelled');
CREATE TYPE public.contact_request_status AS ENUM ('pending', 'contacted', 'resolved');
CREATE TYPE public.consultation_type AS ENUM ('outpatient', 'discharge', 'surgery', 'radiology', 'dermatology', 'cardiology_echo', 'ivf_cycle', 'pathology');


-- ============================================================================
-- 3. CORE TABLES & AUTH INTEGRATION
-- ============================================================================

-- Unified `profiles` table linked to `auth.users`
CREATE TABLE public.profiles (
    id uuid NOT NULL PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    updated_at timestamptz DEFAULT now() NOT NULL,
    created_at timestamptz DEFAULT now() NOT NULL,
    name text NOT NULL,
    email text UNIQUE NOT NULL,
    role public.user_role NOT NULL DEFAULT 'doctor',

    -- Doctor-specific columns
    approved boolean NOT NULL DEFAULT false,
    approved_at timestamptz,
    approved_by uuid REFERENCES public.profiles(id),
    clinic_name text,
    phone text UNIQUE,
    monthly_quota integer NOT NULL DEFAULT 50 CHECK (monthly_quota >= 0),
    quota_used integer NOT NULL DEFAULT 0 CHECK (quota_used >= 0),
    quota_reset_at timestamptz DEFAULT (now() + interval '1 month'),

    -- Referral columns
    referral_code text UNIQUE,
    referred_by uuid REFERENCES public.profiles(id) ON DELETE SET NULL,
    conversion_date timestamptz,
    referral_discount_earned numeric NOT NULL DEFAULT 0,
    total_referrals integer NOT NULL DEFAULT 0,
    successful_referrals integer NOT NULL DEFAULT 0,

    -- Billing columns
    current_plan_id uuid, -- FK added below
    billing_status public.billing_status NOT NULL DEFAULT 'trial',
    trial_ends_at timestamptz,
    last_payment_date timestamptz,
    next_billing_date timestamptz,
    available_discount_amount numeric NOT NULL DEFAULT 0
);
COMMENT ON TABLE public.profiles IS 'Unified table for all users (doctors, admins) linked to auth.users.';

-- Billing Plans Table
CREATE TABLE public.billing_plans (
    id uuid NOT NULL PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    name text NOT NULL,
    description text,
    monthly_price numeric NOT NULL CHECK (monthly_price >= 0),
    quota_limit integer NOT NULL CHECK (quota_limit >= 0),
    features jsonb,
    active boolean NOT NULL DEFAULT true,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now()
);
COMMENT ON TABLE public.billing_plans IS 'Defines the available subscription plans for doctors.';

-- Add FK constraint after tables are created
ALTER TABLE public.profiles
ADD CONSTRAINT profiles_current_plan_id_fkey
FOREIGN KEY (current_plan_id)
REFERENCES public.billing_plans(id) ON DELETE SET NULL;

-- Consultations Table with Full-Text Search
CREATE TABLE public.consultations (
    id uuid NOT NULL PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    doctor_id uuid REFERENCES public.profiles(id) ON DELETE SET NULL,
    submitted_by text NOT NULL,
    patient_name text,
    patient_number integer,
    status public.consultation_status NOT NULL DEFAULT 'pending_generation',
    consultation_type public.consultation_type NOT NULL DEFAULT 'outpatient',
    primary_audio_url text NOT NULL,
    additional_audio_urls jsonb,
    image_urls jsonb,
    ai_generated_note text,
    edited_note text,
    doctor_notes text,
    additional_notes text,
    total_file_size_bytes bigint,
    file_retention_until timestamptz,
    fts tsvector GENERATED ALWAYS AS (
        to_tsvector('english', coalesce(patient_name, '') || ' ' || coalesce(ai_generated_note, '') || ' ' || coalesce(edited_note, ''))
    ) STORED
);
COMMENT ON COLUMN public.consultations.fts IS 'Full-Text Search vector for efficient searching.';

-- Other Tables
CREATE TABLE public.usage_logs (
    id uuid NOT NULL PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    created_at timestamptz NOT NULL DEFAULT now(),
    doctor_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    consultation_id uuid REFERENCES public.consultations(id) ON DELETE SET NULL,
    action_type text NOT NULL,
    quota_before numeric,
    quota_after numeric,
    metadata jsonb
);

CREATE TABLE public.referral_analytics (
    id uuid NOT NULL PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    referrer_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    referred_doctor_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    referral_code text NOT NULL,
    signup_date timestamptz NOT NULL DEFAULT now(),
    conversion_date timestamptz,
    discount_earned numeric NOT NULL DEFAULT 0,
    status text NOT NULL DEFAULT 'pending',
    metadata jsonb
);

CREATE TABLE public.billing_transactions (
    id uuid NOT NULL PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    doctor_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE RESTRICT,
    plan_id uuid REFERENCES public.billing_plans(id) ON DELETE SET NULL,
    amount numeric NOT NULL,
    discount_amount numeric NOT NULL DEFAULT 0,
    final_amount numeric NOT NULL,
    payment_method text,
    payment_status text NOT NULL DEFAULT 'pending',
    payment_date timestamptz,
    billing_period_start timestamptz NOT NULL,
    billing_period_end timestamptz NOT NULL,
    payment_reference text,
    notes text,
    created_by uuid REFERENCES public.profiles(id),
    metadata jsonb
);

CREATE TABLE public.referral_discounts (
    id uuid NOT NULL PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    doctor_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    referral_analytics_id uuid NOT NULL REFERENCES public.referral_analytics(id) ON DELETE CASCADE,
    discount_amount numeric NOT NULL,
    original_amount numeric NOT NULL,
    applied_to_transaction_id uuid REFERENCES public.billing_transactions(id),
    status text NOT NULL DEFAULT 'pending',
    valid_until timestamptz NOT NULL,
    applied_at timestamptz
);

CREATE TABLE public.contact_requests (
    id uuid NOT NULL PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    doctor_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE,
    doctor_name text NOT NULL,
    doctor_email text NOT NULL,
    clinic_name text,
    phone_number text,
    request_type text NOT NULL,
    message text,
    status public.contact_request_status NOT NULL DEFAULT 'pending',
    contacted_at timestamptz,
    resolved_at timestamptz
);


-- ============================================================================
-- 4. SURGICAL INDEXING (Based on Code Analysis)
-- ============================================================================
CREATE INDEX IF NOT EXISTS idx_consultations_doctor_created ON public.consultations (doctor_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles (email);
CREATE INDEX IF NOT EXISTS idx_profiles_referral_code ON public.profiles (referral_code);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_doctor_id ON public.billing_transactions (doctor_id);
CREATE INDEX IF NOT EXISTS idx_usage_logs_doctor_action ON public.usage_logs (doctor_id, action_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_consultations_fts ON public.consultations USING gin(fts);
CREATE INDEX IF NOT EXISTS idx_profiles_referred_by ON public.profiles(referred_by);
CREATE INDEX IF NOT EXISTS idx_profiles_current_plan_id ON public.profiles(current_plan_id);
CREATE INDEX IF NOT EXISTS idx_referral_analytics_referrer_id ON public.referral_analytics(referrer_id);
CREATE INDEX IF NOT EXISTS idx_referral_analytics_referred_doctor_id ON public.referral_analytics(referred_doctor_id);

-- ============================================================================
-- 5. AUTOMATION: FUNCTIONS & TRIGGERS
-- ============================================================================
-- Function to generate a unique referral code
CREATE OR REPLACE FUNCTION public.generate_referral_code(name text)
RETURNS text LANGUAGE plpgsql VOLATILE AS $$
DECLARE
    base_code text;
    final_code text;
    counter integer := 0;
BEGIN
    base_code := UPPER(REGEXP_REPLACE(split_part(name, ' ', 1), '[^a-zA-Z]', '', 'g'));
    IF LENGTH(base_code) < 3 THEN base_code := 'CELER'; END IF;
    LOOP
        final_code := base_code || to_char(now(), 'YY') || (100 + floor(random() * 900))::text || CASE WHEN counter > 0 THEN counter::text ELSE '' END;
        PERFORM 1 FROM public.profiles WHERE referral_code = final_code;
        IF NOT FOUND THEN RETURN final_code; END IF;
        counter := counter + 1;
    END LOOP;
END;
$$;

-- Trigger function to automatically create a profile when a new user signs up.
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, name, referral_code)
    VALUES (new.id, new.email, new.raw_user_meta_data->>'name', public.generate_referral_code(new.raw_user_meta_data->>'name'));
    RETURN new;
END;
$$;
-- SECURE THE FUNCTION: Prevent hijacking by setting a fixed search path.
ALTER FUNCTION public.handle_new_user() SET search_path = public, extensions;

-- Trigger to connect the function to the auth.users table.
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Function to automatically update `updated_at` columns.
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;

-- Apply the trigger to all tables with an `updated_at` column.
CREATE TRIGGER set_updated_at_profiles BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();
CREATE TRIGGER set_updated_at_consultations BEFORE UPDATE ON public.consultations FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();


-- ============================================================================
-- 6. VIEWS FOR PERFORMANCE (Solving N+1 Query Problems)
-- ============================================================================
CREATE OR REPLACE VIEW public.admin_dashboard_summary AS
SELECT
    (SELECT COUNT(*) FROM public.profiles WHERE role = 'doctor') AS total_doctors,
    (SELECT COUNT(*) FROM public.profiles WHERE role = 'doctor' AND approved = false) AS pending_approvals,
    (SELECT COUNT(*) FROM public.profiles WHERE role = 'doctor' AND approved = true) AS approved_doctors,
    (SELECT COUNT(*) FROM public.consultations) AS total_consultations,
    (SELECT COUNT(*) FROM public.usage_logs WHERE action_type = 'ai_generation') AS total_ai_generations,
    (SELECT ROUND((SUM(quota_used)::numeric / NULLIF(SUM(monthly_quota), 0)) * 100, 2) FROM public.profiles WHERE role = 'doctor' AND monthly_quota > 0) AS quota_usage_percentage;

CREATE OR REPLACE VIEW public.admin_doctors_with_stats AS
SELECT
    p.id, p.email, p.name, p.phone, p.clinic_name, p.monthly_quota, p.quota_used, p.quota_reset_at,
    p.approved, p.approved_at, p.referral_code, p.billing_status, p.trial_ends_at, p.created_at, p.updated_at,
    (SELECT name FROM public.profiles ap WHERE ap.id = p.approved_by) AS approved_by,
    (SELECT name FROM public.profiles rp WHERE rp.id = p.referred_by) AS referred_by,
    (SELECT COUNT(*) FROM public.consultations c WHERE c.doctor_id = p.id) AS total_consultations,
    (SELECT COUNT(*) FROM public.usage_logs ul WHERE ul.doctor_id = p.id AND ul.action_type = 'ai_generation' AND ul.created_at >= date_trunc('month', now())) AS this_month_generations,
    (SELECT MAX(ul.created_at) FROM public.usage_logs ul WHERE ul.doctor_id = p.id) AS last_activity,
    COALESCE(ROUND((p.quota_used::numeric / NULLIF(p.monthly_quota, 0)) * 100, 2), 0) AS quota_percentage
FROM public.profiles p
WHERE p.role = 'doctor';


-- ============================================================================
-- 7. ROW-LEVEL SECURITY (RLS) - The Security Foundation
-- ============================================================================
-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.consultations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billing_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billing_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referral_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referral_discounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_requests ENABLE ROW LEVEL SECURITY;

-- Secure helper function to check admin status
CREATE OR REPLACE FUNCTION public.is_admin() RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN RETURN EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'super_admin')); END;
$$;
ALTER FUNCTION public.is_admin() SET search_path = public;

-- POLICIES
-- Profiles: Users manage their own, admins manage all.
CREATE POLICY "Profiles: Users can manage their own profile" ON public.profiles FOR ALL USING (auth.uid() = id);
CREATE POLICY "Profiles: Admins can manage all profiles" ON public.profiles FOR ALL USING (public.is_admin());

-- Consultations: Doctors manage their own, admins can view.
CREATE POLICY "Consultations: Doctors can manage their own" ON public.consultations FOR ALL USING (auth.uid() = doctor_id);
CREATE POLICY "Consultations: Admins can view all" ON public.consultations FOR SELECT USING (public.is_admin());

-- Billing Plans: Authenticated can view, admins can manage.
CREATE POLICY "Billing Plans: Authenticated can view" ON public.billing_plans FOR SELECT TO authenticated USING (true);
CREATE POLICY "Billing Plans: Admins can manage" ON public.billing_plans FOR ALL USING (public.is_admin());

-- Generic Policies for other tables
CREATE POLICY "Billing Transactions: User owns data" ON public.billing_transactions FOR ALL USING (auth.uid() = doctor_id);
CREATE POLICY "Billing Transactions: Admin can manage" ON public.billing_transactions FOR ALL USING (public.is_admin());

CREATE POLICY "Usage Logs: User owns data" ON public.usage_logs FOR ALL USING (auth.uid() = doctor_id);
CREATE POLICY "Usage Logs: Admin can manage" ON public.usage_logs FOR ALL USING (public.is_admin());

CREATE POLICY "Referral Analytics: User owns data" ON public.referral_analytics FOR ALL USING (auth.uid() = referrer_id);
CREATE POLICY "Referral Analytics: Admin can manage" ON public.referral_analytics FOR ALL USING (public.is_admin());

CREATE POLICY "Referral Discounts: User owns data" ON public.referral_discounts FOR ALL USING (auth.uid() = doctor_id);
CREATE POLICY "Referral Discounts: Admin can manage" ON public.referral_discounts FOR ALL USING (public.is_admin());

CREATE POLICY "Contact Requests: User owns data" ON public.contact_requests FOR ALL USING (auth.uid() = doctor_id);
CREATE POLICY "Contact Requests: Admin can manage" ON public.contact_requests FOR ALL USING (public.is_admin());


-- Re-enable notices
SET client_min_messages TO notice;

-- ============================================================================
-- END OF MIGRATION SCRIPT
-- ============================================================================