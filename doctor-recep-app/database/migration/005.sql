-- Celer AI - Migration 003: Harden Profiles and Views
-- This script incorporates the new CTO's feedback for data integrity
-- and is designed to run safely on top of the previous migration.

-- Step 1: Add Integrity-Based CHECK Constraints.
-- This is our database-level safety net. It does not contain business logic,
-- but ensures structural integrity.

-- Remove any potentially older, more complex constraints if they exist.
ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS check_doctor_quota;
ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS check_doctor_billing;
ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS check_doctor_referrals;

-- Add the new, simpler, more robust constraint.
-- It enforces that IF the role is 'doctor', THEN key fields cannot be NULL.
-- It allows any other role to have NULLs without being explicitly named.
ALTER TABLE public.profiles
ADD CONSTRAINT doctor_fields_are_not_null
CHECK (
  (role <> 'doctor') -- This constraint does not apply if the role is NOT 'doctor'
  OR -- If the role IS 'doctor', the following must be true:
  (
    monthly_quota IS NOT NULL AND
    quota_used IS NOT NULL AND
    billing_status IS NOT NULL
  )
);
COMMENT ON CONSTRAINT doctor_fields_are_not_null ON public.profiles IS 'Ensures that core fields for doctors are never NULL, providing a database-level integrity check.';

-- Step 2: Ensure the handle_new_user function is the simplified version.
-- The developer's previous work might have installed the complex version. This will replace it
-- with the correct, simple version that aligns with our strategy.
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  -- Create a basic profile. Table DEFAULTs will handle doctor-specific fields.
  INSERT INTO public.profiles (id, email, name)
  VALUES (new.id, new.email, new.raw_user_meta_data->>'name');

  -- If a role was passed in metadata (e.g., during admin creation), update it.
  IF new.raw_user_meta_data->>'role' IS NOT NULL THEN
    UPDATE public.profiles
    SET role = (new.raw_user_meta_data->>'role')::public.user_role
    WHERE id = new.id;
  END IF;

  RETURN new;
END;
$$;
-- Secure the function with a fixed search path
ALTER FUNCTION public.handle_new_user() SET search_path = public;

-- Step 3: Ensure the admin dashboard views are robust against NULLs.
-- The developer's view definition is good, but we will add COALESCE to make it foolproof.
-- We must DROP and CREATE the view to safely change the column type.

DROP VIEW IF EXISTS public.admin_doctors_with_stats;

CREATE VIEW public.admin_doctors_with_stats
WITH (security_invoker = true) AS
SELECT
    p.id, p.email, p.name, p.phone, p.clinic_name, p.monthly_quota, p.quota_used, p.quota_reset_at,
    p.approved, p.approved_at, p.referral_code, p.billing_status, p.trial_ends_at, p.created_at, p.updated_at,
    (SELECT name FROM public.profiles ap WHERE ap.id = p.approved_by) AS approved_by,
    (SELECT name FROM public.profiles rp WHERE rp.id = p.referred_by) AS referred_by,
    (SELECT COUNT(*) FROM public.consultations c WHERE c.doctor_id = p.id) AS total_consultations,
    (SELECT COUNT(*) FROM public.usage_logs ul WHERE ul.doctor_id = p.id AND ul.action_type = 'ai_generation' AND ul.created_at >= date_trunc('month', now())) AS this_month_generations,
    (SELECT MAX(ul.created_at) FROM public.usage_logs ul WHERE ul.doctor_id = p.id) AS last_activity,
    -- Make quota percentage calculation robust against NULL or zero quota.
    -- The explicit cast to INTEGER is the reason we must DROP and CREATE the view.
    COALESCE(
        CASE
            WHEN p.monthly_quota > 0 THEN ROUND((p.quota_used::numeric * 100) / p.monthly_quota::numeric)
            ELSE 0
        END,
    0)::integer AS quota_percentage
FROM public.profiles p
WHERE p.role = 'doctor';

COMMENT ON VIEW public.admin_doctors_with_stats IS 'Provides a comprehensive overview of doctors with key statistics for the admin dashboard. Re-created to enforce integer type on quota_percentage.';