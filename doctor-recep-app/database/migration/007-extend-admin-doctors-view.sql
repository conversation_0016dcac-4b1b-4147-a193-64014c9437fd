-- Extend admin_doctors_with_stats view to include billing fields
-- This fixes the DoctorBillingInfo interface requirements

-- Drop existing view
DROP VIEW IF EXISTS public.admin_doctors_with_stats;

-- Create extended view with billing fields
CREATE VIEW public.admin_doctors_with_stats
WITH (security_invoker = true) AS
SELECT
    p.id, 
    p.email, 
    p.name, 
    p.phone, 
    p.clinic_name, 
    p.monthly_quota, 
    p.quota_used, 
    p.quota_reset_at,
    p.approved, 
    p.approved_at, 
    p.referral_code, 
    p.billing_status, 
    p.trial_ends_at, 
    p.created_at, 
    p.updated_at,
    -- Billing fields (NEW)
    p.current_plan_id,
    p.last_payment_date,
    p.next_billing_date,
    p.available_discount_amount,
    p.successful_referrals,
    p.referral_discount_earned,
    p.referred_by,
    -- Related data
    (SELECT name FROM public.profiles ap WHERE ap.id = p.approved_by) AS approved_by,
    (SELECT name FROM public.profiles rp WHERE rp.id = p.referred_by) AS referred_by_name,
    -- Stats
    (SELECT COUNT(*) FROM public.consultations c WHERE c.doctor_id = p.id) AS total_consultations,
    (SELECT COUNT(*) FROM public.usage_logs ul WHERE ul.doctor_id = p.id AND ul.action_type = 'ai_generation' AND ul.created_at >= date_trunc('month', now())) AS this_month_generations,
    (SELECT MAX(ul.created_at) FROM public.usage_logs ul WHERE ul.doctor_id = p.id) AS last_activity,
    -- Quota percentage calculation
    COALESCE(
        CASE
            WHEN p.monthly_quota > 0 THEN ROUND((p.quota_used::numeric * 100) / p.monthly_quota::numeric)
            ELSE 0
        END,
    0)::integer AS quota_percentage
FROM public.profiles p
WHERE p.role = 'doctor';

COMMENT ON VIEW public.admin_doctors_with_stats IS 'Extended view with billing fields for DoctorBillingInfo interface. Includes current_plan_id, payment dates, and referral data.';

-- Grant permissions
GRANT SELECT ON public.admin_doctors_with_stats TO authenticated;
GRANT SELECT ON public.admin_doctors_with_stats TO service_role;
