-- Celer AI - Migration 004: Webhook Outbox Queue
-- This migration implements the enterprise-grade webhook outbox pattern
-- to replace the fire-and-forget model with reliable webhook delivery.

-- ============================================================================
-- 1. CREATE WEBHOOK OUTBOX TABLE
-- ============================================================================

CREATE TABLE public.webhook_outbox (
    id uuid NOT NULL PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    
    -- Webhook details
    event_type text NOT NULL,
    payload jsonb NOT NULL,
    webhook_url text NOT NULL,
    headers jsonb DEFAULT '{}'::jsonb,
    
    -- Delivery tracking
    status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'delivered', 'failed', 'dead_letter')),
    attempts integer NOT NULL DEFAULT 0,
    max_attempts integer NOT NULL DEFAULT 5,
    next_attempt_at timestamptz NOT NULL DEFAULT now(),
    
    -- Response tracking
    last_response_status integer,
    last_response_body text,
    last_error text,
    delivered_at timestamptz,
    
    -- Metadata
    source_table text,
    source_id uuid,
    timeout_milliseconds integer DEFAULT 10000
);

-- Add indexes for efficient processing
CREATE INDEX idx_webhook_outbox_status_next_attempt ON public.webhook_outbox (status, next_attempt_at) WHERE status IN ('pending', 'failed');
CREATE INDEX idx_webhook_outbox_created_at ON public.webhook_outbox (created_at);
CREATE INDEX idx_webhook_outbox_source ON public.webhook_outbox (source_table, source_id);

-- Add updated_at trigger
CREATE TRIGGER update_webhook_outbox_updated_at 
    BEFORE UPDATE ON public.webhook_outbox 
    FOR EACH ROW 
    EXECUTE FUNCTION public.update_updated_at_column();

-- ============================================================================
-- 2. WEBHOOK PROCESSING FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION public.process_webhook_outbox()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    webhook_record record;
    response_status integer;
    response_body text;
    error_message text;
    next_attempt timestamptz;
BEGIN
    -- Process pending webhooks that are ready for delivery
    FOR webhook_record IN 
        SELECT * FROM public.webhook_outbox 
        WHERE status IN ('pending', 'failed') 
        AND next_attempt_at <= now()
        AND attempts < max_attempts
        ORDER BY created_at ASC
        LIMIT 10 -- Process in batches to avoid long-running transactions
    LOOP
        BEGIN
            -- Mark as processing
            UPDATE public.webhook_outbox 
            SET 
                status = 'processing',
                attempts = attempts + 1,
                updated_at = now()
            WHERE id = webhook_record.id;

            -- Attempt webhook delivery using pg_net
            SELECT status, content INTO response_status, response_body
            FROM net.http_post(
                url := webhook_record.webhook_url,
                body := webhook_record.payload,
                headers := webhook_record.headers,
                timeout_milliseconds := webhook_record.timeout_milliseconds
            );

            -- Check if delivery was successful (2xx status codes)
            IF response_status >= 200 AND response_status < 300 THEN
                -- Success: Mark as delivered
                UPDATE public.webhook_outbox 
                SET 
                    status = 'delivered',
                    delivered_at = now(),
                    last_response_status = response_status,
                    last_response_body = response_body,
                    updated_at = now()
                WHERE id = webhook_record.id;
                
                RAISE NOTICE 'Webhook % delivered successfully with status %', webhook_record.id, response_status;
            ELSE
                -- Failure: Calculate next attempt with exponential backoff
                next_attempt := now() + (INTERVAL '1 minute' * POWER(2, webhook_record.attempts));
                
                IF webhook_record.attempts >= webhook_record.max_attempts THEN
                    -- Max attempts reached: Move to dead letter
                    UPDATE public.webhook_outbox 
                    SET 
                        status = 'dead_letter',
                        last_response_status = response_status,
                        last_response_body = response_body,
                        last_error = format('Max attempts (%s) reached. Last status: %s', webhook_record.max_attempts, response_status),
                        updated_at = now()
                    WHERE id = webhook_record.id;
                    
                    RAISE WARNING 'Webhook % moved to dead letter after % attempts. Last status: %', 
                        webhook_record.id, webhook_record.max_attempts, response_status;
                ELSE
                    -- Retry later
                    UPDATE public.webhook_outbox 
                    SET 
                        status = 'failed',
                        next_attempt_at = next_attempt,
                        last_response_status = response_status,
                        last_response_body = response_body,
                        last_error = format('HTTP %s: %s', response_status, COALESCE(response_body, 'No response body')),
                        updated_at = now()
                    WHERE id = webhook_record.id;
                    
                    RAISE NOTICE 'Webhook % failed with status %. Next attempt at %', 
                        webhook_record.id, response_status, next_attempt;
                END IF;
            END IF;

        EXCEPTION WHEN OTHERS THEN
            -- Handle unexpected errors
            error_message := SQLERRM;
            next_attempt := now() + (INTERVAL '1 minute' * POWER(2, webhook_record.attempts));
            
            IF webhook_record.attempts >= webhook_record.max_attempts THEN
                UPDATE public.webhook_outbox 
                SET 
                    status = 'dead_letter',
                    last_error = format('Exception after %s attempts: %s', webhook_record.max_attempts, error_message),
                    updated_at = now()
                WHERE id = webhook_record.id;
                
                RAISE WARNING 'Webhook % moved to dead letter due to exception: %', webhook_record.id, error_message;
            ELSE
                UPDATE public.webhook_outbox 
                SET 
                    status = 'failed',
                    next_attempt_at = next_attempt,
                    last_error = error_message,
                    updated_at = now()
                WHERE id = webhook_record.id;
                
                RAISE NOTICE 'Webhook % failed with exception: %. Next attempt at %', 
                    webhook_record.id, error_message, next_attempt;
            END IF;
        END;
    END LOOP;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.process_webhook_outbox() TO postgres;

-- ============================================================================
-- 3. HELPER FUNCTION TO QUEUE WEBHOOKS
-- ============================================================================

CREATE OR REPLACE FUNCTION public.queue_webhook(
    p_event_type text,
    p_payload jsonb,
    p_webhook_url text,
    p_headers jsonb DEFAULT '{"Content-Type": "application/json"}'::jsonb,
    p_source_table text DEFAULT NULL,
    p_source_id uuid DEFAULT NULL,
    p_max_attempts integer DEFAULT 5,
    p_timeout_milliseconds integer DEFAULT 10000
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    webhook_id uuid;
BEGIN
    INSERT INTO public.webhook_outbox (
        event_type,
        payload,
        webhook_url,
        headers,
        source_table,
        source_id,
        max_attempts,
        timeout_milliseconds
    ) VALUES (
        p_event_type,
        p_payload,
        p_webhook_url,
        p_headers,
        p_source_table,
        p_source_id,
        p_max_attempts,
        p_timeout_milliseconds
    ) RETURNING id INTO webhook_id;
    
    RETURN webhook_id;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.queue_webhook(text, jsonb, text, jsonb, text, uuid, integer, integer) TO postgres, authenticated;

-- ============================================================================
-- 4. UPDATE CONTACT_REQUESTS TRIGGER TO USE OUTBOX
-- ============================================================================

-- First, create the new trigger function that uses the outbox
CREATE OR REPLACE FUNCTION public.notify_contact_request_via_outbox()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    webhook_url text;
    webhook_headers jsonb;
    webhook_payload jsonb;
BEGIN
    -- Only trigger for new contact requests
    IF TG_OP = 'INSERT' THEN
        -- Build webhook URL (you'll need to set this to your actual endpoint)
        webhook_url := 'https://your-domain.com/api/notify/admin';
        
        -- Build headers with authentication
        webhook_headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || COALESCE(current_setting('app.webhook_secret_token', true), 'your-webhook-secret')
        );
        
        -- Build payload
        webhook_payload := jsonb_build_object(
            'event_type', 'new_contact_request',
            'data', row_to_json(NEW)
        );
        
        -- Queue the webhook instead of sending directly
        PERFORM public.queue_webhook(
            p_event_type := 'new_contact_request',
            p_payload := webhook_payload,
            p_webhook_url := webhook_url,
            p_headers := webhook_headers,
            p_source_table := 'contact_requests',
            p_source_id := NEW.id
        );
        
        RAISE NOTICE 'Queued webhook for new contact request: %', NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Drop the old trigger if it exists and create the new one
DROP TRIGGER IF EXISTS notify_new_contact_request ON public.contact_requests;
CREATE TRIGGER notify_new_contact_request
    AFTER INSERT ON public.contact_requests
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_contact_request_via_outbox();

-- ============================================================================
-- 5. SCHEDULE WEBHOOK PROCESSING JOB
-- ============================================================================

-- Note: This requires pg_cron extension and should be run in Supabase Dashboard
-- The following is the SQL command to run in the Supabase SQL Editor:

/*
-- Schedule webhook processing to run every minute
SELECT cron.schedule(
    'process-webhook-outbox',
    '* * * * *', -- Every minute
    'SELECT public.process_webhook_outbox();'
);
*/

-- ============================================================================
-- 6. CLEANUP AND MONITORING FUNCTIONS
-- ============================================================================

-- Function to clean up old delivered webhooks (run weekly)
CREATE OR REPLACE FUNCTION public.cleanup_webhook_outbox()
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    deleted_count integer;
BEGIN
    -- Delete delivered webhooks older than 30 days
    DELETE FROM public.webhook_outbox 
    WHERE status = 'delivered' 
    AND delivered_at < now() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RAISE NOTICE 'Cleaned up % old webhook records', deleted_count;
    RETURN deleted_count;
END;
$$;

-- Function to get webhook outbox statistics
CREATE OR REPLACE FUNCTION public.get_webhook_outbox_stats()
RETURNS TABLE (
    status text,
    count bigint,
    oldest_created_at timestamptz,
    newest_created_at timestamptz
)
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT 
        status,
        COUNT(*) as count,
        MIN(created_at) as oldest_created_at,
        MAX(created_at) as newest_created_at
    FROM public.webhook_outbox 
    GROUP BY status
    ORDER BY status;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.cleanup_webhook_outbox() TO postgres;
GRANT EXECUTE ON FUNCTION public.get_webhook_outbox_stats() TO postgres, authenticated;

-- ============================================================================
-- 7. COMMENTS AND DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE public.webhook_outbox IS 'Outbox pattern implementation for reliable webhook delivery';
COMMENT ON FUNCTION public.process_webhook_outbox() IS 'Processes pending webhooks with exponential backoff and dead letter handling';
COMMENT ON FUNCTION public.queue_webhook(text, jsonb, text, jsonb, text, uuid, integer, integer) IS 'Helper function to queue webhooks for reliable delivery';
COMMENT ON FUNCTION public.notify_contact_request_via_outbox() IS 'Trigger function that queues webhooks for new contact requests';
