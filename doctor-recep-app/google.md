Login with Google
Supabase Auth supports Sign in with Google for the web, native Android applications, and Chrome extensions.

Prerequisites#

A Google Cloud project. Go to the Google Cloud Platform and create a new project if necessary.
Configuration#

To support Sign In with Google, you need to configure the Google provider for your Supabase project.


Web

Expo React Native

Flutter

Swift

Android (Kotlin)

Chrome Extensions
For web applications, you can set up your signin button two different ways:

Use your own application code for the button.
Use Google's pre-built sign-in or One Tap flows.
Application code configuration#

To use your own application code:

In the Google Cloud console, go to the Consent Screen configuration page. The consent screen is the view shown to your users when they consent to signing in to your app.

Under Authorized domains, add your Supabase project's domain, which has the form <PROJECT_ID>.supabase.co.

Configure the following non-sensitive scopes:

.../auth/userinfo.email
...auth/userinfo.profile
openid
Go to the API Credentials page.

Click Create credentials and choose OAuth Client ID.

For application type, choose Web application.

Under Authorized JavaScript origins, add your site URL.

Under Authorized redirect URLs, enter the callback URL from the Supabase dashboard. Expand the Google Auth Provider section to display it.

The redirect URL is visible to your users. You can customize it by configuring custom domains.
When you finish configuring your credentials, you will be shown your client ID and secret. Add these to the Google Auth Provider section of the Supabase Dashboard.

Alternatively, you can configure Google authentication using the Management API:

1
2
3
4
5
6
7
8
9
10
11
12
13
# First, get your access token from https://supabase.com/dashboard/account/tokens
export SUPABASE_ACCESS_TOKEN="your-access-token"
export PROJECT_REF="your-project-ref"
# Update auth config to enable Google provider
curl -X PATCH "https://api.supabase.com/v1/projects/$PROJECT_REF/config/auth" \
  -H "Authorization: Bearer $SUPABASE_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "external_google_enabled": true,
    "external_google_client_id": "your-google-client-id",
    "external_google_secret": "your-google-client-secret"
  }'
In local development, you can add the client ID and secret to your config.toml file.
Google pre-built configuration#

To use Google's pre-built signin buttons:

In the Google Cloud console, go to the Consent Screen configuration page. The consent screen is the view shown to your users when they consent to signing in to your app.
Configure the screen to your liking, making sure you add links to your app's privacy policy and terms of service.
Go to the API Credentials page.
Click Create credentials and choose OAuth Client ID.
For application type, choose Web application.
Under Authorized JavaScript origins and Authorized redirect URLs, add your site URL. This is the URL of the website where the signin button will appear, not your Supabase project domain. If you're testing in localhost, ensure that you have http://localhost set in the Authorized JavaScript origins section as well. This is important when integrating with Google One-Tap to ensure you can use it locally.
When you finish configuring your credentials, you will be shown your client ID. Add this to the Client IDs field in the Google Auth Provider section of the Supabase Dashboard. Leave the OAuth client ID and secret blank. You don't need them when using Google's pre-built approach.
Signing users in#


Web

Expo React Native

Flutter

Android (Kotlin)

Chrome Extensions
Application code#

To use your own application code for the signin button, call the signInWithOAuth method (or the equivalent for your language).

Make sure you're using the right supabase client in the following code.
If you're not using Server-Side Rendering or cookie-based Auth, you can directly use the createClient from @supabase/supabase-js. If you're using Server-Side Rendering, see the Server-Side Auth guide for instructions on creating your Supabase client.
1
2
3
supabase.auth.signInWithOAuth({
  provider: 'google',
})
For an implicit flow, that's all you need to do. The user will be taken to Google's consent screen, and finally redirected to your app with an access and refresh token pair representing their session.

For a PKCE flow, for example in Server-Side Auth, you need an extra step to handle the code exchange. When calling signInWithOAuth, provide a redirectTo URL which points to a callback route. This redirect URL should be added to your redirect allow list.


Client

Server
In the server, you need to handle the redirect to the OAuth provider's authentication endpoint. The signInWithOAuth method returns the endpoint URL, which you can redirect to.

1
2
3
4
5
6
7
8
9
10
const { data, error } = await supabase.auth.signInWithOAuth({
  provider,
  options: {
    redirectTo: 'http://example.com/auth/callback',
  },
})
if (data.url) {
  redirect(data.url) // use the redirect API for your server framework
}
At the callback endpoint, handle the code exchange to save the user session.


Next.js

SvelteKit

Astro

Remix

Express
Create a new file at app/auth/callback/route.ts and populate with the following:

app/auth/callback/route.ts

1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
import { NextResponse } from 'next/server'
// The client you created from the Server-Side Auth instructions
import { createClient } from '@/utils/supabase/server'
export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  // if "next" is in param, use it as the redirect URL
  let next = searchParams.get('next') ?? '/'
  if (!next.startsWith('/')) {
    // if "next" is not a relative URL, use the default
    next = '/'
  }
  if (code) {
    const supabase = await createClient()
    const { error } = await supabase.auth.exchangeCodeForSession(code)
    if (!error) {
      const forwardedHost = request.headers.get('x-forwarded-host') // original origin before load balancer
      const isLocalEnv = process.env.NODE_ENV === 'development'
      if (isLocalEnv) {
        // we can be sure that there is no load balancer in between, so no need to watch for X-Forwarded-Host
        return NextResponse.redirect(`${origin}${next}`)
      } else if (forwardedHost) {
        return NextResponse.redirect(`https://${forwardedHost}${next}`)
      } else {
        return NextResponse.redirect(`${origin}${next}`)
      }
    }
  }
  // return the user to an error page with instructions
  return NextResponse.redirect(`${origin}/auth/auth-code-error`)
}
After a successful code exchange, the user's session will be saved to cookies.

Saving Google tokens#

The tokens saved by your application are the Supabase Auth tokens. Your app might additionally need the Google OAuth 2.0 tokens to access Google services on the user's behalf.

On initial login, you can extract the provider_token from the session and store it in a secure storage medium. The session is available in the returned data from signInWithOAuth (implicit flow) and exchangeCodeForSession (PKCE flow).

Google does not send out a refresh token by default, so you will need to pass parameters like these to signInWithOAuth() in order to extract the provider_refresh_token:

1
2
3
4
5
6
7
8
9
const { data, error } = await supabase.auth.signInWithOAuth({
  provider: 'google',
  options: {
    queryParams: {
      access_type: 'offline',
      prompt: 'consent',
    },
  },
})
Google pre-built#

Most web apps and websites can utilize Google's personalized sign-in buttons, One Tap or automatic sign-in for the best user experience.

Load the Google client library in your app by including the third-party script:

1
<script src="https://accounts.google.com/gsi/client" async></script>
Use the HTML Code Generator to customize the look, feel, features and behavior of the Sign in with Google button.

Pick the Swap to JavaScript callback option, and input the name of your callback function. This function will receive a CredentialResponse when sign in completes.

To make your app compatible with Chrome's third-party-cookie phase-out, make sure to set data-use_fedcm_for_prompt to true.

Your final HTML code might look something like this:

1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
<div
  id="g_id_onload"
  data-client_id="<client ID>"
  data-context="signin"
  data-ux_mode="popup"
  data-callback="handleSignInWithGoogle"
  data-nonce=""
  data-auto_select="true"
  data-itp_support="true"
  data-use_fedcm_for_prompt="true"
></div>
<div
  class="g_id_signin"
  data-type="standard"
  data-shape="pill"
  data-theme="outline"
  data-text="signin_with"
  data-size="large"
  data-logo_alignment="left"
></div>
Create a handleSignInWithGoogle function that takes the CredentialResponse and passes the included token to Supabase. The function needs to be available in the global scope for Google's code to find it.

1
2
3
4
5
6
async function handleSignInWithGoogle(response) {
  const { data, error } = await supabase.auth.signInWithIdToken({
    provider: 'google',
    token: response.credential,
  })
}
(Optional) Configure a nonce. The use of a nonce is recommended for extra security, but optional. The nonce should be generated randomly each time, and it must be provided in both the data-nonce attribute of the HTML code and the options of the callback function.

1
2
3
4
5
6
7
async function handleSignInWithGoogle(response) {
  const { data, error } = await supabase.auth.signInWithIdToken({
    provider: 'google',
    token: response.credential,
    nonce: '<NONCE>',
  })
}
Note that the nonce should be the same in both places, but because Supabase Auth expects the provider to hash it (SHA-256, hexadecimal representation), you need to provide a hashed version to Google and a non-hashed version to signInWithIdToken.

You can get both versions by using the in-built crypto library:

1
2
3
4
5
6
7
8
9
10
11
12
// Adapted from https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/digest#converting_a_digest_to_a_hex_string
const nonce = btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(32))))
const encoder = new TextEncoder()
const encodedNonce = encoder.encode(nonce)
crypto.subtle.digest('SHA-256', encodedNonce).then((hashBuffer) => {
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashedNonce = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')
})
// Use 'hashedNonce' when making the authentication request to Google
// Use 'nonce' when invoking the supabase.auth.signInWithIdToken() method
One-tap with Next.js#

If you're integrating Google One-Tap with your Next.js application, you can refer to the example below to get started:

1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
'use client'
import Script from 'next/script'
import { createClient } from '@/utils/supabase/client'
import { CredentialResponse } from 'google-one-tap'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
const OneTapComponent = () => {
  const supabase = createClient()
  const router = useRouter()
  // generate nonce to use for google id token sign-in
  const generateNonce = async (): Promise<string[]> => {
    const nonce = btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(32))))
    const encoder = new TextEncoder()
    const encodedNonce = encoder.encode(nonce)
    const hashBuffer = await crypto.subtle.digest('SHA-256', encodedNonce)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    const hashedNonce = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')
    return [nonce, hashedNonce]
  }
  useEffect(() => {
    const initializeGoogleOneTap = () => {
      console.log('Initializing Google One Tap')
      window.addEventListener('load', async () => {
        const [nonce, hashedNonce] = await generateNonce()
        console.log('Nonce: ', nonce, hashedNonce)
        // check if there's already an existing session before initializing the one-tap UI
        const { data, error } = await supabase.auth.getSession()
        if (error) {
          console.error('Error getting session', error)
        }
        if (data.session) {
          router.push('/')
          return
        }
        /* global google */
        google.accounts.id.initialize({
          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
          callback: async (response: CredentialResponse) => {
            try {
              // send id token returned in response.credential to supabase
              const { data, error } = await supabase.auth.signInWithIdToken({
                provider: 'google',
                token: response.credential,
                nonce,
              })
              if (error) throw error
              console.log('Session data: ', data)
              console.log('Successfully logged in with Google One Tap')
              // redirect to protected page
              router.push('/')
            } catch (error) {
              console.error('Error logging in with Google One Tap', error)
            }
          },
          nonce: hashedNonce,
          // with chrome's removal of third-party cookies, we need to use FedCM instead (https://developers.google.com/identity/gsi/web/guides/fedcm-migration)
          use_fedcm_for_prompt: true,
        })
        google.accounts.id.prompt() // Display the One Tap UI
      })
    }
    initializeGoogleOneTap()
    return () => window.removeEventListener('load', initializeGoogleOneTap)
  }, [])
  return (
    <>
      <Script src="https://accounts.google.com/gsi/client" />
      <div id="oneTap" className="fixed top-0 right-0 z-[100]" />
    </>
  )
}
export default OneTapComponent
Google consent screen#

Google Consent Screen

By default, the Google consent screen shows the root domain of the callback URL, where Google will send the authentication response. With Supabase Auth, it is your Supabase project's domain (https://<your-project-ref>.supabase.co).

If that is not preferable, you can use a Custom Domain with your Supabase project. You can use it as your project's domain when creating the Supabase client in your application and initiating the authentication flow. It will then show up in the Google consent screen. If you want your app name and the logo on the consent screen, you must submit your app to Google for verification.


Understand the One Tap user experience

bookmark_border
 


This guide contains detailed descriptions of the One Tap user experience, including when One Tap is or is not displayed and user session behaviors.

Globally opt out

Users can opt out of One Tap if they disable the Google Account sign-in prompts flag in the Apps with access to your account page. The opted-out sessions aren't shown in One Tap. If all Google sessions are opted out, One Tap doesn't display.

If a user disables third-party sign-in on browsers with FedCM enabled, One Tap is not displayed. In Chrome settings under the Privacy and Security section users control the display of third-party sign-in prompts either globally or for individual sites.

Exponential cooldown

If the user closes the One Tap prompt manually, the One Tap prompt is suppressed. A user closes One Tap when they tap Close close in the top-right corner of the prompt, after which One Tap wouldn't display in the same browser or the last website visited for a period of time.

The following exponential time periods are used for cooldowns when FedCM is not enabled:

Consecutive times closed	Time period that One Tap is disabled
1	Two hours
2	One day
3	One week
4+	Four weeks
The cooldown status resets after a successful sign-in using One Tap or the Sign in with Google button.

When FedCM is enabled, browser vendors may define their own, different, cooldown time periods.

Note: When FedCM is enabled, Chrome users can reset cooldown status by clicking on the lock icon in the address bar and clicking the Reset Permission button.
Auto-dismissal on mobile browsers

On mobile browsers, and when FedCM is not enabled, Google One Tap closes automatically after a short time period unless the user directly interacts with the One Tap UI.

The threshold for auto-dismissal is 90 seconds. This is subject to change.

Note: Auto-dismissal doesn't trigger a cooldown. Auto-dismissal doesn't happen on desktop, iPad, and tablet browsers.
Show a dialog to prevent unintended clicks

Objective: It's important that users understand the purpose of One Tap. They should be fully aware of the call to action when they give their consent to create an account, since they must share some personal information, such as their first name, last name, email, and profile picture.
One Tap now comes with different security measures to enforce the integrity of the dialog, but some browsers don't support these capabilities. Unsupported browsers include non-Chromium-based ones or those before v75. In these cases, or if the dialog is covered with other content, a pop-up window is displayed that requests the user's consent to create an account.

**Figure 3.** Sign-in dialog box

Upgraded UX on ITP browsers

Due to Intelligent Tracking Prevention (ITP), the normal One Tap UX doesn't work on Chrome on iOS, Safari, or Firefox. A different UX is provided instead on these browsers. You have the option to disable this UX on ITP browsers by setting the data-itp_support attribute.

The upgraded One Tap UX on ITP browsers begins with a welcome page as shown below. After the user selects Continue, a pop-up window is opened. The UX in the pop-up window is very similar to normal One Tap.

**Figure 4.** Welcome Page

When there is no Google session, after the 'Continue' button is clicked, users must first sign in to their Google Account. See One Tap support on ITP browsers for more details.

Note: To avoid the flashing pop-up issue, auto sign in isn't supported.
Key user journeys

Note: Refer to Automatic sign-in key user journeys for the UX flow when automatic sign-in is enabled.
The user journeys vary based on the following statuses.

Session status on Google websites. The following terms are used to indicate different Google session status when the user journey starts.

Single-session: There is exactly one active session on Google websites.
Multiple-session: There are more than one active sessions on Google websites.
Key Point: One Tap is not shown if there is no active session on Google websites.
Whether the selected Google Account has approved your website when the user journey starts. The following terms are used to indicate different approval status.

New user: The selected account hasn't approved your website.
Returning user: The selected account has approved your website before.
Single-session new user journey

The new user consent page.

Google One Tap consent page
The second confirmation dialog in a pop-up window for non-Chromium browsers.

Consent page in a pop-up window for non-Chromium browsers.
After users confirm, an ID token is shared with your website.
Single-session returning user journey

The returning user page.

One Tap returning user page
After users click the button, an ID token is shared with your website.
Multiple-session new user journey

The account chooser page.

Account Chooser Page
The consent page.

Google One Tap consent page

For non-Chromium browsers, this confirmation dialog is displayed in a pop-up window:

Consent page in a pop-up window for non-Chromium browsers.
After user consent, an ID token is shared with your website.
Multiple-session returning user journey

The account chooser page.

Account Chooser Page
After users select a returning account, an ID token is shared with your website